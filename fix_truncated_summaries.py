#!/usr/bin/env python3
"""
Fix truncated contextual summaries in existing JSON file
"""

import json
import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add the parent directory to the path to access agentic_chunker
sys.path.insert(0, str(Path(__file__).parent.parent))

from agentic_chunker import AgenticChunker, ChunkingConfig, DocumentSection

def fix_truncated_summaries():
    """Fix truncated contextual summaries in test_chunks_5.json."""
    
    print("🔧 Fixing Truncated Contextual Summaries")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Get API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ GOOGLE_API_KEY not found in environment variables")
        print("   Please set your Google API key in the .env file")
        return 1
    
    # Input and output files
    input_file = "test_chunks_5.json"
    output_file = "test_chunks_5_fixed.json"
    
    if not Path(input_file).exists():
        print(f"❌ Input file {input_file} not found!")
        return 1
    
    # Load existing chunks
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    chunks = data.get('chunks', [])
    print(f"📖 Loaded {len(chunks)} chunks from {input_file}")
    
    # Create chunker configuration
    config = ChunkingConfig(
        api_key=api_key,
        model_name="gemini-2.0-flash-exp"
    )
    
    # Initialize chunker
    chunker = AgenticChunker(config)
    
    # Process each chunk to regenerate full summaries
    fixed_chunks = []
    
    for i, chunk_data in enumerate(chunks, 1):
        print(f"\n🔄 Processing chunk {i}/{len(chunks)}: {chunk_data['id']}")
        
        try:
            # Get content and sections
            content = chunk_data.get('content', '')
            sections_data = chunk_data.get('sections', [])
            
            # Convert section data to DocumentSection objects
            sections = []
            for section_data in sections_data:
                section = DocumentSection(
                    level=section_data['level'],
                    number=section_data['number'],
                    title=section_data.get('title', ''),
                    content=section_data['content'],
                    line_start=section_data['line_start'],
                    line_end=section_data['line_end'],
                    parent=section_data.get('parent')
                )
                sections.append(section)
            
            # Generate new full contextual summary (no truncation)
            print(f"   🧠 Generating full contextual summary...")
            new_summary = chunker._generate_chunk_summary(content, sections)
            
            # Check if it's different from the truncated version
            old_summary = chunk_data.get('contextual_summary', '')
            if old_summary.endswith('...'):
                print(f"   ✅ Fixed truncated summary (was {len(old_summary)} chars, now {len(new_summary)} chars)")
            else:
                print(f"   📝 Regenerated summary ({len(new_summary)} chars)")
            
            # Update the chunk data with the new summary
            chunk_data['contextual_summary'] = new_summary
            fixed_chunks.append(chunk_data)
            
        except Exception as e:
            print(f"   ❌ Error processing chunk: {str(e)}")
            # Keep original chunk if there's an error
            fixed_chunks.append(chunk_data)
    
    # Update the data structure
    data['chunks'] = fixed_chunks
    
    # Add fix information to metadata
    from datetime import datetime
    if 'summary_generation' not in data:
        data['summary_generation'] = {}
    
    data['summary_generation'].update({
        'fixed_truncation': True,
        'fixed_at': datetime.now().isoformat(),
        'total_summaries_fixed': len(fixed_chunks)
    })
    
    # Save fixed file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(data, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ Fixed summaries saved to: {output_file}")
    
    # Replace original file
    Path(input_file).rename(f"{input_file}.backup")
    Path(output_file).rename(input_file)
    print(f"📁 Replaced {input_file} (backup saved as {input_file}.backup)")
    
    # Show sample of improved summary
    if fixed_chunks:
        sample_chunk = fixed_chunks[0]
        summary = sample_chunk['contextual_summary']
        print(f"\n📝 Sample improved summary for {sample_chunk['id']}:")
        print(f"   Length: {len(summary)} characters")
        print(f"   Preview: {summary[:200]}{'...' if len(summary) > 200 else ''}")
    
    return 0

if __name__ == "__main__":
    sys.exit(fix_truncated_summaries())