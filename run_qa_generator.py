#!/usr/bin/env python3
"""
Easy-to-use script for running the QA Generator with parameterized LLM selection
"""

import os
import sys
import argparse
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider, QuestionDifficulty, QuestionType

def get_provider_config(provider_name: str, api_key: str = None, model_name: str = None) -> dict:
    """Get provider configuration with defaults."""
    
    configs = {
        'google': {
            'api_key_env': 'GOOGLE_API_KEY',
            'model_default': 'gemini-2.0-flash-exp',
            'model_env': 'GOOGLE_MODEL'
        },
        'openai': {
            'api_key_env': 'OPENAI_API_KEY',
            'model_default': 'gpt-4o',
            'model_env': 'OPENAI_MODEL'
        },
        'anthropic': {
            'api_key_env': 'ANTHROPIC_API_KEY',
            'model_default': 'claude-3-5-sonnet-20241022',
            'model_env': 'ANTHROPIC_MODEL'
        },
        'lm_studio': {
            'api_key_env': 'LM_STUDIO_API_KEY',  # Optional
            'model_default': 'cognitivecomputations/dolphin-mistral-24b-venice-edition',
            'model_env': 'LM_STUDIO_MODEL'
        },
        'openrouter': {
            'api_key_env': 'OPENROUTER_API_KEY',
            'model_default': 'deepseek/deepseek-chat-v3.1:free',
            'model_env': 'OPENROUTER_MODEL'
        }
    }
    
    if provider_name not in configs:
        raise ValueError(f"Unsupported provider: {provider_name}")
    
    config = configs[provider_name]
    
    # Get API key
    final_api_key = api_key or os.getenv(config['api_key_env'], "")
    
    # Get model name
    final_model = model_name or os.getenv(config['model_env'], config['model_default'])
    
    return {
        'api_key': final_api_key,
        'model_name': final_model,
        'requires_api_key': provider_name not in ['lm_studio']
    }

def main():
    """Main function with parameterized LLM selection."""
    
    # Parse command line arguments
    parser = argparse.ArgumentParser(
        description='QA Generator for Legal Documents with parameterized LLM selection',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Use LM Studio (default from .env)
  python run_qa_generator.py
  
  # Use Google Gemini
  python run_qa_generator.py --provider google --api-key YOUR_KEY
  
  # Use OpenAI with custom model
  python run_qa_generator.py --provider openai --api-key YOUR_KEY --model gpt-4
  
  # Use OpenRouter with DeepSeek (free)
  python run_qa_generator.py --provider openrouter --api-key YOUR_OPENROUTER_KEY
  
  # Use specific input file
  python run_qa_generator.py --input test_chunks_5.json --provider lm_studio
"""
    )
    
    parser.add_argument(
        '--provider',
        choices=['google', 'openai', 'anthropic', 'lm_studio', 'openrouter'],
        help='LLM provider (default: from LLM_PROVIDER env var or google)'
    )
    
    parser.add_argument(
        '--api-key',
        help='API key for the LLM provider'
    )
    
    parser.add_argument(
        '--model',
        help='Model name to use'
    )
    
    parser.add_argument(
        '--input',
        help='Input chunks file (default: auto-detect)'
    )
    
    parser.add_argument(
        '--output',
        default='qa_triplets_output.json',
        help='Output file (default: qa_triplets_output.json)'
    )
    
    parser.add_argument(
        '--questions-per-chunk',
        type=int,
        help='Number of questions per chunk'
    )
    
    parser.add_argument(
        '--batch-size',
        type=int,
        help='Batch size for processing'
    )
    
    args = parser.parse_args()
    
    print("🤖 QA Generator for Legal Documents")
    print("=" * 40)
    
    # Load environment variables
    load_dotenv()
    
    # Get LLM provider configuration
    provider_name = args.provider or os.getenv('LLM_PROVIDER', 'google').lower()
    
    try:
        provider = LLMProvider(provider_name)
    except ValueError:
        print(f"❌ Invalid LLM provider: {provider_name}")
        print("   Supported providers: google, openai, anthropic, lm_studio, openrouter")
        return 1
    
    # Get provider configuration
    try:
        provider_config = get_provider_config(provider_name, args.api_key, args.model)
    except ValueError as e:
        print(f"❌ {str(e)}")
        return 1
    
    # Validate API key for cloud providers
    if provider_config['requires_api_key'] and not provider_config['api_key']:
        print(f"❌ API key required for {provider_name}")
        print(f"   Use --api-key argument or set environment variable")
        return 1
    
    # Create configuration
    config = QAGenerationConfig(
        llm_provider=provider,
        api_key=provider_config['api_key'],
        model_name=provider_config['model_name'],
        base_url=os.getenv('LM_STUDIO_BASE_URL', 'http://localhost:1234/v1'),
        questions_per_chunk=args.questions_per_chunk or int(os.getenv('QUESTIONS_PER_CHUNK', 2)),
        batch_size=args.batch_size or int(os.getenv('BATCH_SIZE', 10)),
        enable_cross_chunk=os.getenv('ENABLE_CROSS_CHUNK', 'true').lower() == 'true',
        enable_cross_section=os.getenv('ENABLE_CROSS_SECTION', 'true').lower() == 'true',
        max_cross_references=int(os.getenv('MAX_CROSS_REFERENCES', 3)),
        output_format=os.getenv('OUTPUT_FORMAT', 'json'),
        validate_answers=os.getenv('VALIDATE_ANSWERS', 'true').lower() == 'true',
        min_answer_length=int(os.getenv('MIN_ANSWER_LENGTH', 50)),
        max_answer_length=int(os.getenv('MAX_ANSWER_LENGTH', 500))
    )
    
    # Set difficulty distribution
    config.difficulty_distribution = {
        QuestionDifficulty.EASY: float(os.getenv('DIFFICULTY_EASY', 0.3)),
        QuestionDifficulty.MEDIUM: float(os.getenv('DIFFICULTY_MEDIUM', 0.4)),
        QuestionDifficulty.HARD: float(os.getenv('DIFFICULTY_HARD', 0.2)),
        QuestionDifficulty.VERY_HARD: float(os.getenv('DIFFICULTY_VERY_HARD', 0.1))
    }
    
    # Set question type distribution
    config.question_type_distribution = {
        QuestionType.FACTUAL: float(os.getenv('QUESTION_TYPE_FACTUAL', 0.3)),
        QuestionType.ANALYTICAL: float(os.getenv('QUESTION_TYPE_ANALYTICAL', 0.2)),
        QuestionType.REASONING: float(os.getenv('QUESTION_TYPE_REASONING', 0.2)),
        QuestionType.CROSS_SECTION: float(os.getenv('QUESTION_TYPE_CROSS_SECTION', 0.1)),
        QuestionType.CROSS_CHUNK: float(os.getenv('QUESTION_TYPE_CROSS_CHUNK', 0.1)),
        QuestionType.COMPARATIVE: float(os.getenv('QUESTION_TYPE_COMPARATIVE', 0.05)),
        QuestionType.INFERENTIAL: float(os.getenv('QUESTION_TYPE_INFERENTIAL', 0.05)),
        QuestionType.APPLICATION: float(os.getenv('QUESTION_TYPE_APPLICATION', 0.0))
    }
    
    # Get input file
    if args.input:
        input_file = args.input
        if not os.path.exists(input_file):
            print(f"❌ Input file not found: {input_file}")
            return 1
    else:
        # Auto-detect input file
        input_candidates = [
            '../chunks_with_summaries.json',
            '../chunks_output.json', 
            'chunks_with_summaries.json',
            'chunks_output.json',
            'test_chunks_5.json'  # Include test file
        ]
        
        input_file = None
        for candidate in input_candidates:
            if os.path.exists(candidate):
                input_file = candidate
                break
        
        if not input_file:
            print("❌ No chunks file found. Please run the chunker first or specify --input:")
            print("   cd .. && python run_chunker.py")
            print("   or use: python run_qa_generator.py --input your_chunks.json")
            return 1
    
    # Set output file
    output_file = args.output
    
    print(f"📋 Configuration:")
    print(f"   LLM Provider: {provider_name}")
    print(f"   Model: {provider_config['model_name']}")
    print(f"   Input: {input_file}")
    print(f"   Output: {output_file}")
    print(f"   Questions per chunk: {config.questions_per_chunk}")
    print(f"   Batch size: {config.batch_size}")
    print(f"   Cross-chunk enabled: {config.enable_cross_chunk}")
    print()
    
    try:
        # Initialize QA generator
        print("🚀 Initializing QA Generator...")
        generator = QAGenerator(config)
        
        # Load chunks
        print(f"📖 Loading chunks from {input_file}...")
        chunks = generator.load_chunks(input_file)
        print(f"   Loaded {len(chunks)} chunks")
        
        # Generate QA triplets
        print("🧠 Generating QA triplets...")
        triplets = generator.generate_qa_triplets()
        
        # Save results
        print(f"💾 Saving results to {output_file}...")
        generator.save_qa_triplets(output_file)
        
        print(f"\n✅ Success! Generated {len(triplets)} QA triplets")
        print(f"📁 Output saved to: {output_file}")
        
        # Print summary statistics
        difficulty_counts = {}
        type_counts = {}
        
        for triplet in triplets:
            diff = triplet.difficulty.value
            q_type = triplet.question_type.value
            difficulty_counts[diff] = difficulty_counts.get(diff, 0) + 1
            type_counts[q_type] = type_counts.get(q_type, 0) + 1
        
        print(f"\n📊 Generation Summary:")
        print(f"   Total QA triplets: {len(triplets)}")
        print(f"   Chunks processed: {len(chunks)}")
        
        print(f"\n   Difficulty distribution:")
        for diff, count in difficulty_counts.items():
            print(f"     {diff}: {count}")
        
        print(f"\n   Question type distribution:")
        for q_type, count in type_counts.items():
            print(f"     {q_type}: {count}")
        
        # Show example triplet
        if triplets:
            example = triplets[0]
            print(f"\n📋 Example QA Triplet:")
            print(f"   ID: {example.id}")
            print(f"   Difficulty: {example.difficulty.value}")
            print(f"   Type: {example.question_type.value}")
            print(f"   Question: {example.question[:100]}...")
            print(f"   Answer: {example.answer[:100]}...")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())