#!/usr/bin/env python3
"""
Demo script showing the complete QA Generator workflow
"""

import os
import sys
from pathlib import Path

def demo_qa_generator():
    """Demonstrate the QA Generator system."""
    
    print("🤖 QA Generator Demo")
    print("=" * 30)
    
    print("This system generates diverse question-answer-context triplets from legal document chunks.")
    print("It supports multiple LLM providers and various question types and difficulty levels.")
    print()
    
    # Check for required input files
    parent_dir = Path(__file__).parent.parent
    input_candidates = [
        parent_dir / "chunks_with_summaries.json",
        parent_dir / "chunks_output.json"
    ]
    
    input_file = None
    for candidate in input_candidates:
        if candidate.exists():
            input_file = candidate
            break
    
    if input_file:
        print(f"📁 Found chunks file: {input_file.name}")
    else:
        print("❌ No chunks file found. Please run the chunker first:")
        print("   cd .. && python run_chunker.py")
        return
    
    print()
    print("🎯 QA Generation Features:")
    
    print("\n📊 Question Difficulty Levels:")
    print("   • Easy: Basic facts and definitions")
    print("   • Medium: Conceptual understanding")
    print("   • Hard: Analytical reasoning")  
    print("   • Very Hard: Complex legal synthesis")
    
    print("\n🧠 Question Types:")
    print("   • Factual: Direct information retrieval")
    print("   • Analytical: Deep legal analysis")
    print("   • Reasoning: Complex logical inference") 
    print("   • Cross-Section: Multi-article synthesis")
    print("   • Cross-Chunk: Multi-chunk integration")
    print("   • Comparative: Legal distinctions")
    print("   • Inferential: Implied meanings")
    print("   • Application: Practical scenarios")
    
    print("\n🔧 Supported LLM Providers:")
    print("   • Google Gemini 2.0 Flash (fast, cost-effective)")
    print("   • OpenAI GPT-4o (high-quality reasoning)")
    print("   • Anthropic Claude 3.5 Sonnet (analytical)")
    
    print("\n📋 Sample QA Triplet Format:")
    print("""   {
     "id": "qa_abc123def456",
     "question": "What principle does Article 2 establish?",
     "answer": "Article 2 establishes non-retroactivity...",
     "context": "Art. 2. La loi ne dispose que pour l'avenir...",
     "difficulty": "medium",
     "question_type": "factual",
     "chunk_ids": ["chunk_0001"],
     "metadata": {...},
     "created_at": "2025-09-02T12:00:00Z"
   }""")
    
    print("\n🚀 Quick Start:")
    print("   1. Copy .env.example to .env")
    print("   2. Add your API key (Google/OpenAI/Anthropic)")
    print("   3. Run: python run_qa_generator.py")
    
    print("\n⚙️ Configuration Options:")
    print("   • Choose LLM provider and model")
    print("   • Set questions per chunk (default: 3)")
    print("   • Configure difficulty distribution")
    print("   • Enable/disable cross-chunk questions")
    print("   • Customize question type weights")
    
    print("\n📈 Typical Output:")
    if input_file:
        try:
            import json
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            chunk_count = len(data.get('chunks', []))
            estimated_questions = chunk_count * 3 + (chunk_count // 2)
            print(f"   • Input: {chunk_count} chunks")
            print(f"   • Estimated output: ~{estimated_questions} QA triplets")
            print(f"   • Generation time: ~{chunk_count * 2} minutes (with API calls)")
        except Exception:
            print("   • Estimated: 200+ QA triplets from typical legal document")
    
    print("\n🎯 Use Cases:")
    print("   • Legal research and training")
    print("   • Document comprehension testing")
    print("   • RAG system question generation")
    print("   • Educational content creation")
    print("   • Compliance training materials")

if __name__ == "__main__":
    demo_qa_generator()