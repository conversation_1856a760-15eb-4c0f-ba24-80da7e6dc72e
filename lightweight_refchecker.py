#!/usr/bin/env python3
"""
Lightweight RefChecker Alternative
=================================

A lightweight alternative to RefChecker that provides claim-level validation
without requiring PyTorch or complex dependencies. Uses LLM APIs directly
for claim extraction and validation.
"""

import json
import re
import os
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class ValidationResult(Enum):
    """Validation results for claims."""
    FACTUAL = "factual"
    HALLUCINATED = "hallucinated"
    NEUTRAL = "neutral"

@dataclass
class Claim:
    """Represents an extracted claim."""
    content: str
    position: int
    validation_result: ValidationResult
    confidence_score: float = 0.0
    reasoning: str = ""

@dataclass
class ValidationReport:
    """Validation report for a QA triplet."""
    qa_triplet_id: str
    total_claims: int
    factual_claims: int
    hallucinated_claims: int
    neutral_claims: int
    overall_factuality_score: float
    claims: List[Claim]
    passed_validation: bool
    validation_timestamp: str
    
    @property
    def hallucination_rate(self) -> float:
        if self.total_claims == 0:
            return 0.0
        return self.hallucinated_claims / self.total_claims

class LightweightRefChecker:
    """Lightweight RefChecker implementation using OpenAI API."""
    
    def __init__(self, api_key: str, model: str = "gpt-4o-mini", factuality_threshold: float = 0.7):
        self.api_key = api_key
        self.model = model
        self.factuality_threshold = factuality_threshold
        
        # Set up OpenAI
        try:
            import openai
            self.client = openai.OpenAI(api_key=api_key)
        except ImportError:
            raise ImportError("OpenAI package required. Install with: pip install openai")
    
    def extract_claims(self, answer: str, question: str = "") -> List[str]:
        """Extract claims from answer using LLM."""
        prompt = f"""Extract all factual claims from the following answer. Return each claim as a separate sentence.

Question: {question}
Answer: {answer}

Please list each claim separately, one per line. Focus on factual statements that can be verified against source material.

Claims:"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=500
            )
            
            claims_text = response.choices[0].message.content.strip()
            claims = [claim.strip() for claim in claims_text.split('\n') if claim.strip()]
            return claims
        except Exception as e:
            print(f"Warning: Claim extraction failed: {e}")
            # Fallback: split by sentence
            sentences = re.split(r'[.!?]+', answer)
            return [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    
    def validate_claim(self, claim: str, context: str) -> Tuple[ValidationResult, float, str]:
        """Validate a single claim against context."""
        prompt = f"""Validate if the following claim is supported by the given context.

Context:
{context}

Claim to validate:
{claim}

Please analyze if this claim is:
1. FACTUAL - The claim is directly supported by the context
2. HALLUCINATED - The claim contradicts the context or makes unsupported assertions
3. NEUTRAL - Cannot determine from the context

Respond in this format:
RESULT: [FACTUAL/HALLUCINATED/NEUTRAL]
CONFIDENCE: [0.0-1.0]
REASONING: [Brief explanation]"""

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.1,
                max_tokens=300
            )
            
            result_text = response.choices[0].message.content.strip()
            
            # Parse response
            result = ValidationResult.NEUTRAL
            confidence = 0.5
            reasoning = "Unknown"
            
            for line in result_text.split('\n'):
                if line.startswith('RESULT:'):
                    result_str = line.split(':', 1)[1].strip().upper()
                    if 'FACTUAL' in result_str:
                        result = ValidationResult.FACTUAL
                    elif 'HALLUCINATED' in result_str:
                        result = ValidationResult.HALLUCINATED
                    else:
                        result = ValidationResult.NEUTRAL
                elif line.startswith('CONFIDENCE:'):
                    try:
                        confidence = float(line.split(':', 1)[1].strip())
                    except:
                        confidence = 0.5
                elif line.startswith('REASONING:'):
                    reasoning = line.split(':', 1)[1].strip()
            
            return result, confidence, reasoning
            
        except Exception as e:
            print(f"Warning: Claim validation failed: {e}")
            return ValidationResult.NEUTRAL, 0.0, f"Validation error: {e}"
    
    def validate_qa_triplet(self, qa_triplet: Dict[str, Any]) -> ValidationReport:
        """Validate a QA triplet."""
        qa_id = qa_triplet.get("id", "unknown")
        question = qa_triplet.get("question", "")
        answer = qa_triplet.get("answer", "")
        context = qa_triplet.get("context", [])
        
        # Extract context text
        context_text = self._extract_context_text(context)
        
        if not answer or not context_text:
            return self._create_empty_report(qa_id)
        
        # Extract claims
        print(f"  Extracting claims for {qa_id}...")
        claim_texts = self.extract_claims(answer, question)
        
        if not claim_texts:
            return self._create_empty_report(qa_id)
        
        # Validate each claim
        print(f"  Validating {len(claim_texts)} claims...")
        claims = []
        factual_count = 0
        hallucinated_count = 0
        neutral_count = 0
        
        for i, claim_text in enumerate(claim_texts):
            result, confidence, reasoning = self.validate_claim(claim_text, context_text)
            
            claim = Claim(
                content=claim_text,
                position=i,
                validation_result=result,
                confidence_score=confidence,
                reasoning=reasoning
            )
            claims.append(claim)
            
            if result == ValidationResult.FACTUAL:
                factual_count += 1
            elif result == ValidationResult.HALLUCINATED:
                hallucinated_count += 1
            else:
                neutral_count += 1
        
        # Calculate metrics
        total_claims = len(claims)
        factuality_score = factual_count / total_claims if total_claims > 0 else 0.0
        passed = factuality_score >= self.factuality_threshold
        
        return ValidationReport(
            qa_triplet_id=qa_id,
            total_claims=total_claims,
            factual_claims=factual_count,
            hallucinated_claims=hallucinated_count,
            neutral_claims=neutral_count,
            overall_factuality_score=factuality_score,
            claims=claims,
            passed_validation=passed,
            validation_timestamp=datetime.now().isoformat()
        )
    
    def _extract_context_text(self, context) -> str:
        """Extract text from context."""
        if isinstance(context, str):
            return context
        elif isinstance(context, list):
            text_parts = []
            for item in context:
                if isinstance(item, dict) and "content" in item:
                    text_parts.append(item["content"])
                elif isinstance(item, str):
                    text_parts.append(item)
            return "\n".join(text_parts)
        else:
            return str(context)
    
    def _create_empty_report(self, qa_id: str) -> ValidationReport:
        """Create empty validation report."""
        return ValidationReport(
            qa_triplet_id=qa_id,
            total_claims=0,
            factual_claims=0,
            hallucinated_claims=0,
            neutral_claims=0,
            overall_factuality_score=0.0,
            claims=[],
            passed_validation=False,
            validation_timestamp=datetime.now().isoformat()
        )

def generate_refchecker_report(qa_triplets: List[Dict], api_key: str, model: str = "gpt-4o-mini") -> str:
    """Generate RefChecker-style validation report."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"refchecker_validation_report_{timestamp}.json"
    
    print(f"🔍 Lightweight RefChecker Validation")
    print(f"Using model: {model}")
    print(f"Validating {len(qa_triplets)} QA triplets...")
    
    checker = LightweightRefChecker(api_key, model)
    reports = []
    
    for i, qa_triplet in enumerate(qa_triplets, 1):
        print(f"\n[{i}/{len(qa_triplets)}] Validating {qa_triplet.get('id', 'unknown')}...")
        report = checker.validate_qa_triplet(qa_triplet)
        reports.append(report)
    
    # Calculate summary stats
    total = len(reports)
    passed = sum(1 for r in reports if r.passed_validation)
    failed = total - passed
    avg_factuality = sum(r.overall_factuality_score for r in reports) / total if total > 0 else 0
    
    # Create comprehensive report
    report_data = {
        "validation_timestamp": datetime.now().isoformat(),
        "model_used": model,
        "total_triplets_validated": total,
        "passed_validation": passed,
        "failed_validation": failed,
        "success_rate": passed / total if total > 0 else 0,
        "average_factuality_score": avg_factuality,
        "config": {
            "model_name": model,
            "factuality_threshold": checker.factuality_threshold
        },
        "detailed_reports": [
            {
                "qa_triplet_id": r.qa_triplet_id,
                "passed_validation": r.passed_validation,
                "total_claims": r.total_claims,
                "factual_claims": r.factual_claims,
                "hallucinated_claims": r.hallucinated_claims,
                "neutral_claims": r.neutral_claims,
                "overall_factuality_score": r.overall_factuality_score,
                "hallucination_rate": r.hallucination_rate,
                "validation_timestamp": r.validation_timestamp,
                "claims": [
                    {
                        "content": claim.content,
                        "validation_result": claim.validation_result.value,
                        "confidence_score": claim.confidence_score,
                        "reasoning": claim.reasoning
                    }
                    for claim in r.claims
                ]
            }
            for r in reports
        ]
    }
    
    # Save report
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n📊 Validation Summary:")
    print(f"  Total triplets: {total}")
    print(f"  ✅ Passed: {passed} ({passed/total*100:.1f}%)")
    print(f"  ❌ Failed: {failed} ({failed/total*100:.1f}%)")
    print(f"  📈 Average factuality: {avg_factuality:.2f}")
    
    # Show failed triplets
    failed_reports = [r for r in reports if not r.passed_validation]
    if failed_reports:
        print(f"\n❌ Failed Triplets (contradictory claims):")
        for report in failed_reports[:5]:
            print(f"  - {report.qa_triplet_id}: Factuality {report.overall_factuality_score:.2f}")
            hallucinated_claims = [c for c in report.claims if c.validation_result == ValidationResult.HALLUCINATED]
            for claim in hallucinated_claims[:2]:
                print(f"    • Hallucinated: {claim.content[:80]}...")
    
    print(f"\n📄 Report saved to: {report_file}")
    return report_file

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Lightweight RefChecker validation')
    parser.add_argument('--qa-file', required=True, help='QA triplets JSON file')
    parser.add_argument('--api-key', help='OpenAI API key')
    parser.add_argument('--model', default='gpt-4o-mini', help='OpenAI model to use')
    
    args = parser.parse_args()
    
    # Get API key
    api_key = args.api_key or os.getenv('OPENAI_API_KEY')
    if not api_key:
        print("❌ Error: OpenAI API key required")
        return 1
    
    try:
        # Load QA triplets
        with open(args.qa_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        qa_triplets = data.get('qa_triplets', [])
        
        # Generate report
        report_file = generate_refchecker_report(qa_triplets, api_key, args.model)
        return 0
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit(main())