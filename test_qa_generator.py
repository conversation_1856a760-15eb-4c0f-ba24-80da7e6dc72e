#!/usr/bin/env python3
"""
Test script for the QA Generator
Tests functionality without requiring API keys
"""

import sys
import os
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider, QuestionDifficulty, QuestionType

def create_test_chunks_file():
    """Create a temporary test chunks file."""
    test_chunks = {
        "config": {},
        "total_chunks": 2,
        "created_at": "2025-09-02T12:00:00.000000",
        "chunks": [
            {
                "id": "chunk_0001",
                "content": "Art. 1. Test article about civil rights and obligations in Luxembourg law.",
                "contextual_summary": "This chunk covers basic civil rights provisions.",
                "sections": [
                    {
                        "level": "article",
                        "number": "1",
                        "title": "Basic Rights",
                        "content": "Art. 1. Test article about civil rights and obligations in Luxembourg law.",
                        "line_start": 1,
                        "line_end": 2,
                        "parent": None
                    }
                ],
                "metadata": {
                    "chunk_id": 1,
                    "section_count": 1
                },
                "word_count": 12,
                "character_count": 65
            },
            {
                "id": "chunk_0002", 
                "content": "Art. 2. Additional provisions regarding legal procedures and court jurisdiction.",
                "contextual_summary": "This chunk establishes procedural requirements.",
                "sections": [
                    {
                        "level": "article",
                        "number": "2",
                        "title": "Procedures",
                        "content": "Art. 2. Additional provisions regarding legal procedures and court jurisdiction.",
                        "line_start": 3,
                        "line_end": 4,
                        "parent": None
                    }
                ],
                "metadata": {
                    "chunk_id": 2,
                    "section_count": 1
                },
                "word_count": 10,
                "character_count": 75
            }
        ]
    }
    
    # Create temporary file
    temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8')
    json.dump(test_chunks, temp_file, indent=2)
    temp_file.close()
    
    return temp_file.name

def test_qa_generator_loading():
    """Test QA generator initialization and chunk loading."""
    print("Testing QA Generator initialization...")
    
    # Create mock configuration
    config = QAGenerationConfig(
        llm_provider=LLMProvider.GOOGLE,
        api_key="test_key",
        model_name="test_model"
    )
    
    try:
        # Mock the LLM provider to avoid API calls
        with patch('qa_generator.LLMProviderFactory.create_provider') as mock_factory:
            mock_provider = Mock()
            mock_provider.validate_configuration.return_value = True
            mock_factory.return_value = mock_provider
            
            # Initialize generator
            generator = QAGenerator(config)
            
            # Test chunk loading
            test_file = create_test_chunks_file()
            chunks = generator.load_chunks(test_file)
            
            print(f"✅ Successfully loaded {len(chunks)} chunks")
            
            # Verify chunk data
            if len(chunks) == 2:
                first_chunk = chunks[0]
                print(f"   First chunk ID: {first_chunk.id}")
                print(f"   First chunk content: {first_chunk.content[:50]}...")
                print(f"   First chunk summary: {first_chunk.contextual_summary}")
                
            # Clean up
            os.unlink(test_file)
            
            return True
            
    except Exception as e:
        print(f"❌ Error in QA generator test: {str(e)}")
        return False

def test_question_generation_mock():
    """Test question generation with mocked LLM responses."""
    print("\nTesting question generation with mocked responses...")
    
    config = QAGenerationConfig(
        llm_provider=LLMProvider.GOOGLE,
        api_key="test_key",
        model_name="test_model",
        questions_per_chunk=2
    )
    
    try:
        # Create mock LLM response
        mock_response = '''
        {
            "question": "What are the basic civil rights established in Article 1?",
            "answer": "Article 1 establishes fundamental civil rights and obligations for all citizens in Luxembourg law.",
            "reasoning": "This answer directly references the content of Article 1 which discusses civil rights provisions."
        }
        '''
        
        with patch('qa_generator.LLMProviderFactory.create_provider') as mock_factory:
            mock_provider = Mock()
            mock_provider.validate_configuration.return_value = True
            mock_provider.generate_content.return_value = mock_response
            mock_factory.return_value = mock_provider
            
            # Initialize generator
            generator = QAGenerator(config)
            
            # Load test chunks
            test_file = create_test_chunks_file()
            chunks = generator.load_chunks(test_file)
            
            # Generate QA triplets with mocked responses
            triplets = generator.generate_qa_triplets()
            
            print(f"✅ Successfully generated {len(triplets)} QA triplets")
            
            if triplets:
                example = triplets[0]
                print(f"   Example question: {example.question}")
                print(f"   Example answer: {example.answer[:100]}...")
                print(f"   Difficulty: {example.difficulty.value}")
                print(f"   Type: {example.question_type.value}")
            
            # Clean up
            os.unlink(test_file)
            
            return True
            
    except Exception as e:
        print(f"❌ Error in question generation test: {str(e)}")
        return False

def test_configuration_validation():
    """Test configuration validation."""
    print("\nTesting configuration validation...")
    
    try:
        # Test valid configuration
        config = QAGenerationConfig(
            llm_provider=LLMProvider.GOOGLE,
            api_key="test_key",
            model_name="test_model"
        )
        
        print(f"✅ Configuration created successfully")
        print(f"   Provider: {config.llm_provider.value}")
        print(f"   Questions per chunk: {config.questions_per_chunk}")
        print(f"   Cross-chunk enabled: {config.enable_cross_chunk}")
        
        # Check difficulty distribution sums to 1.0
        total_difficulty = sum(config.difficulty_distribution.values())
        print(f"   Difficulty distribution sum: {total_difficulty:.2f}")
        
        # Check question type distribution
        total_types = sum(config.question_type_distribution.values())
        print(f"   Question type distribution sum: {total_types:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in configuration test: {str(e)}")
        return False

def test_output_format():
    """Test output format and file saving."""
    print("\nTesting output format...")
    
    try:
        # Create a minimal triplet for testing
        from qa_generator import QATriplet
        from datetime import datetime
        
        test_triplet = QATriplet(
            id="test_001",
            question="What is a test question?",
            answer="This is a test answer to demonstrate the format.",
            context="Test context for the question.",
            difficulty=QuestionDifficulty.EASY,
            question_type=QuestionType.FACTUAL,
            chunk_ids=["chunk_0001"],
            metadata={"test": "metadata"},
            created_at=datetime.now().isoformat()
        )
        
        # Test JSON serialization
        triplet_dict = {
            'id': test_triplet.id,
            'question': test_triplet.question,
            'answer': test_triplet.answer,
            'context': test_triplet.context,
            'difficulty': test_triplet.difficulty.value,
            'question_type': test_triplet.question_type.value,
            'chunk_ids': test_triplet.chunk_ids,
            'metadata': test_triplet.metadata,
            'created_at': test_triplet.created_at
        }
        
        # Test JSON serialization
        json_str = json.dumps(triplet_dict, indent=2)
        
        print(f"✅ Output format validation successful")
        print(f"   JSON length: {len(json_str)} characters")
        print(f"   Sample output:\n{json_str[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in output format test: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("QA Generator Test Suite")
    print("=" * 30)
    
    all_tests_passed = True
    
    # Test 1: Basic loading and initialization
    if not test_qa_generator_loading():
        all_tests_passed = False
    
    # Test 2: Question generation with mocks
    if not test_question_generation_mock():
        all_tests_passed = False
    
    # Test 3: Configuration validation
    if not test_configuration_validation():
        all_tests_passed = False
    
    # Test 4: Output format
    if not test_output_format():
        all_tests_passed = False
    
    print("\n" + "=" * 30)
    if all_tests_passed:
        print("✅ All tests passed!")
        print("\n🚀 Next steps:")
        print("   1. Copy .env.example to .env")
        print("   2. Add your API key to .env")
        print("   3. Run: python run_qa_generator.py")
    else:
        print("❌ Some tests failed!")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())