#!/usr/bin/env python3
"""
Test script to verify the evidence field in QA generation
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_evidence_field():
    """Test that the evidence field is properly included in QA triplets."""
    
    print("🧪 Testing Evidence Field in QA Generation")
    print("=" * 50)
    
    try:
        from qa_generator import QATriplet, QuestionDifficulty, QuestionType
        
        # Test creating a QATriplet with evidence
        test_triplet = QATriplet(
            id="test_evidence_001",
            question="What is the evidence field test?",
            answer="This tests the evidence field functionality.",
            context=[{"content": "Test context content", "line_start": 1, "line_end": 5}],
            difficulty=QuestionDifficulty.MEDIUM,
            question_type=QuestionType.FACTUAL,
            chunk_ids=["test_chunk_001"],
            context_spans=["span_1_5"],
            evidence=["exact phrase from context", "another supporting phrase"],
            metadata={"test": True},
            created_at=datetime.now().isoformat()
        )
        
        print("✅ QATriplet with evidence field created successfully")
        print(f"   Evidence: {test_triplet.evidence}")
        
        # Test JSON serialization
        triplet_dict = {
            'id': test_triplet.id,
            'question': test_triplet.question,
            'answer': test_triplet.answer,
            'context': test_triplet.context,
            'difficulty': test_triplet.difficulty.value,
            'question_type': test_triplet.question_type.value,
            'chunk_ids': test_triplet.chunk_ids,
            'context_spans': test_triplet.context_spans,
            'evidence': test_triplet.evidence,  # Test evidence field
            'metadata': test_triplet.metadata,
            'created_at': test_triplet.created_at
        }
        
        json_str = json.dumps(triplet_dict, indent=2, ensure_ascii=False)
        print("✅ JSON serialization with evidence field successful")
        print(f"   JSON contains evidence: {'evidence' in json_str}")
        
        # Verify structure
        parsed_json = json.loads(json_str)
        assert 'evidence' in parsed_json
        assert isinstance(parsed_json['evidence'], list)
        assert len(parsed_json['evidence']) == 2
        
        print("✅ Evidence field structure validation passed")
        print("📋 Sample JSON structure:")
        print(json_str[:500] + "...")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing evidence field: {str(e)}")
        return False

def test_prompt_updates():
    """Test that prompt templates include evidence requirements."""
    
    print("\n🔍 Testing Updated Prompt Templates")
    print("=" * 40)
    
    try:
        from question_engine import PromptTemplates
        
        templates = PromptTemplates()
        
        # Check that key templates include evidence
        templates_to_check = [
            ('FACTUAL_EASY', templates.FACTUAL_EASY),
            ('FACTUAL_MEDIUM', templates.FACTUAL_MEDIUM),
            ('ANALYTICAL_HARD', templates.ANALYTICAL_HARD),
            ('REASONING_VERY_HARD', templates.REASONING_VERY_HARD),
            ('CROSS_SECTION', templates.CROSS_SECTION),
            ('CROSS_CHUNK', templates.CROSS_CHUNK)
        ]
        
        for name, template in templates_to_check:
            has_evidence = 'evidence' in template.lower()
            status = "✅" if has_evidence else "❌"
            print(f"   {status} {name}: {'Contains evidence field' if has_evidence else 'Missing evidence field'}")
        
        print("✅ Prompt template verification completed")
        return True
        
    except Exception as e:
        print(f"❌ Error testing prompt templates: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Evidence Field Test Suite")
    print()
    
    success1 = test_evidence_field()
    success2 = test_prompt_updates()
    
    print("\n📊 Test Results:")
    print(f"   Evidence Field Creation: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Prompt Template Updates: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1 and success2:
        print("\n🎉 All evidence field tests passed!")
        print("💡 The QA generator now includes evidence phrases from context in the JSON output.")
    else:
        print("\n⚠️ Some tests failed. Please check the implementation.")
    
    sys.exit(0 if (success1 and success2) else 1)