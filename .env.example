# QA Generator Configuration
# Copy this file to .env and fill in your API keys

# LLM Provider Selection (google, openai, anthropic, lm_studio)
LLM_PROVIDER=lm_studio

# API Keys (only provide the one you're using)
GOOGLE_API_KEY=your_google_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# LM Studio Configuration
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_MODEL=cognitivecomputations/dolphin-mistral-24b-venice-edition

# Model Configuration
GOOGLE_MODEL=gemini-2.0-flash-exp
OPENAI_MODEL=gpt-4o
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022

# QA Generation Parameters
QUESTIONS_PER_CHUNK=2
BATCH_SIZE=10
ENABLE_CROSS_CHUNK=true
ENABLE_CROSS_SECTION=true
MAX_CROSS_REFERENCES=3

# Question Distribution (percentages should sum to 1.0)
DIFFICULTY_EASY=0.3
DIFFICULTY_MEDIUM=0.4
DIFFICULTY_HARD=0.2
DIFFICULTY_VERY_HARD=0.1

QUESTION_TYPE_FACTUAL=0.3
QUESTION_TYPE_ANALYTICAL=0.2
QUESTION_TYPE_REASONING=0.2
QUESTION_TYPE_CROSS_SECTION=0.1
QUESTION_TYPE_CROSS_CHUNK=0.1
QUESTION_TYPE_COMPARATIVE=0.05
QUESTION_TYPE_INFERENTIAL=0.05
QUESTION_TYPE_APPLICATION=0.0

# Output Configuration
OUTPUT_FORMAT=json
VALIDATE_ANSWERS=true
MIN_ANSWER_LENGTH=50
MAX_ANSWER_LENGTH=500