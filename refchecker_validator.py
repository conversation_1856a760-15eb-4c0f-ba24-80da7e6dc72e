"""
RefChecker Validator for QA Generator
====================================

This module integrates Amazon's RefChecker for fine-grained hallucination detection
in generated question-answer pairs. It validates that all claims in generated answers
are properly entailed by the source context passages.

Features:
- Claim extraction from generated answers
- Hallucination detection using multiple checker methods
- Integration with existing QA triplet structure
- Detailed validation reports with claim-level analysis
- Support for different RefChecker models and configurations
"""

from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json
import logging
from datetime import datetime
import os
import warnings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ValidationResult(Enum):
    """Validation results for claims."""
    FACTUAL = "factual"           # ✅ Claim is supported by context
    HALLUCINATED = "hallucinated" # ❌ Claim contradicts or isn't supported by context
    NEUTRAL = "neutral"           # ❓ Cannot determine factuality


class CheckerType(Enum):
    """Types of RefChecker validators available."""
    LLM_CHECKER = "llm"          # LLM-based checker (GPT-4, Claude, etc.)
    ALIGNSCORE = "alignscore"    # AlignScore-based checker
    NLI = "nli"                  # Natural Language Inference checker


@dataclass
class Claim:
    """Represents an extracted claim from a generated answer."""
    content: str
    position: int  # Position in the original text
    validation_result: ValidationResult
    confidence_score: float = 0.0
    supporting_evidence: List[str] = None
    
    def __post_init__(self):
        if self.supporting_evidence is None:
            self.supporting_evidence = []


@dataclass
class ValidationReport:
    """Comprehensive validation report for a QA triplet."""
    qa_triplet_id: str
    total_claims: int
    factual_claims: int
    hallucinated_claims: int
    neutral_claims: int
    overall_factuality_score: float
    claims: List[Claim]
    passed_validation: bool
    validation_timestamp: str
    checker_type: CheckerType
    model_used: str = ""
    
    @property
    def hallucination_rate(self) -> float:
        """Calculate the hallucination rate."""
        if self.total_claims == 0:
            return 0.0
        return self.hallucinated_claims / self.total_claims
    
    @property
    def factuality_rate(self) -> float:
        """Calculate the factuality rate."""
        if self.total_claims == 0:
            return 0.0
        return self.factual_claims / self.total_claims


@dataclass
class RefCheckerConfig:
    """Configuration for RefChecker validation."""
    checker_type: CheckerType = CheckerType.LLM_CHECKER
    model_name: str = "gpt-4o"
    api_key: str = ""
    api_base: str = ""
    
    # Validation parameters
    batch_size: int = 8
    max_new_tokens: int = 1000
    claim_format: str = "triplet"  # "triplet" or "subsentence"
    
    # Thresholds
    factuality_threshold: float = 0.7  # Minimum factuality score to pass
    max_hallucination_rate: float = 0.3  # Maximum allowed hallucination rate
    
    # Processing options
    enable_localization: bool = True  # Map claims back to source text
    max_reference_segment_length: int = 0  # 0 = no limit
    
    def validate(self) -> bool:
        """Validate the configuration."""
        if self.checker_type == CheckerType.LLM_CHECKER and not self.api_key:
            logger.warning("API key required for LLM checker")
            return False
        return True


class RefCheckerValidator:
    """
    RefChecker-based validator for QA triplets.
    
    This class provides fine-grained hallucination detection by:
    1. Extracting claims from generated answers
    2. Checking each claim against source context
    3. Providing detailed validation reports
    4. Filtering out hallucinated QA pairs
    """
    
    def __init__(self, config: RefCheckerConfig):
        """Initialize the RefChecker validator."""
        self.config = config
        self.extractor = None
        self.checker = None
        
        # Validate configuration
        if not config.validate():
            raise ValueError("Invalid RefChecker configuration")
        
        self._initialize_components()
    
    def _initialize_components(self):
        """Initialize RefChecker extractor and checker components."""
        try:
            from refchecker import LLMExtractor, LLMChecker, AlignScoreChecker, NLIChecker
            
            if self.config.checker_type == CheckerType.LLM_CHECKER:
                # Set up environment variables for API access
                if self.config.api_key:
                    if "gpt" in self.config.model_name.lower():
                        os.environ["OPENAI_API_KEY"] = self.config.api_key
                    elif "claude" in self.config.model_name.lower():
                        os.environ["ANTHROPIC_API_KEY"] = self.config.api_key
                
                # Initialize LLM-based extractor and checker
                extractor_kwargs = {
                    "model": self.config.model_name,
                    "batch_size": self.config.batch_size
                }
                checker_kwargs = {
                    "model": self.config.model_name,
                    "batch_size": self.config.batch_size
                }
                
                if self.config.api_base:
                    extractor_kwargs["api_base"] = self.config.api_base
                    checker_kwargs["api_base"] = self.config.api_base
                
                self.extractor = LLMExtractor(**extractor_kwargs)
                self.checker = LLMChecker(**checker_kwargs)
                
            elif self.config.checker_type == CheckerType.ALIGNSCORE:
                self.extractor = LLMExtractor(
                    model=self.config.model_name,
                    batch_size=self.config.batch_size
                )
                self.checker = AlignScoreChecker(
                    device=0,
                    batch_size=self.config.batch_size
                )
                
            elif self.config.checker_type == CheckerType.NLI:
                self.extractor = LLMExtractor(
                    model=self.config.model_name,
                    batch_size=self.config.batch_size
                )
                self.checker = NLIChecker(
                    device=0,
                    batch_size=self.config.batch_size
                )
            
            logger.info(f"Initialized RefChecker with {self.config.checker_type.value} checker")
            
        except ImportError as e:
            logger.error(f"RefChecker not installed: {e}")
            raise ImportError(
                "RefChecker not installed. Please run: pip install refchecker && python -m spacy download en_core_web_sm"
            )
        except Exception as e:
            logger.error(f"Failed to initialize RefChecker: {e}")
            raise
    
    def validate_qa_triplet(self, qa_triplet: Dict[str, Any]) -> ValidationReport:
        """
        Validate a single QA triplet for hallucinations.
        
        Args:
            qa_triplet: QA triplet dictionary with id, question, answer, context
            
        Returns:
            ValidationReport with detailed claim-level analysis
        """
        try:
            qa_id = qa_triplet.get("id", "unknown")
            question = qa_triplet.get("question", "")
            answer = qa_triplet.get("answer", "")
            context = qa_triplet.get("context", "")
            
            # Prepare context text
            context_text = self._extract_context_text(context)
            
            if not answer or not context_text:
                logger.warning(f"Empty answer or context for QA triplet {qa_id}")
                return self._create_empty_report(qa_id)
            
            # Extract claims from the answer
            extraction_results = self.extractor.extract(
                batch_responses=[answer],
                batch_questions=[question] if question else None,
                max_new_tokens=self.config.max_new_tokens
            )
            
            if not extraction_results or not extraction_results[0].claims:
                logger.warning(f"No claims extracted from answer for QA triplet {qa_id}")
                return self._create_empty_report(qa_id)
            
            # Prepare claims for checking
            claims_content = [claim.content for claim in extraction_results[0].claims]
            
            # Check claims against context
            validation_results = self.checker.check(
                batch_claims=[claims_content],
                batch_references=[context_text],
                max_reference_segment_length=self.config.max_reference_segment_length
            )
            
            # Process validation results
            claims = self._process_validation_results(
                extraction_results[0].claims,
                validation_results[0] if validation_results else []
            )
            
            # Calculate metrics
            total_claims = len(claims)
            factual_claims = sum(1 for c in claims if c.validation_result == ValidationResult.FACTUAL)
            hallucinated_claims = sum(1 for c in claims if c.validation_result == ValidationResult.HALLUCINATED)
            neutral_claims = sum(1 for c in claims if c.validation_result == ValidationResult.NEUTRAL)
            
            # Calculate overall factuality score
            if total_claims > 0:
                overall_score = factual_claims / total_claims
            else:
                overall_score = 0.0
            
            # Determine if validation passed
            passed = (
                overall_score >= self.config.factuality_threshold and
                (hallucinated_claims / total_claims if total_claims > 0 else 0) <= self.config.max_hallucination_rate
            )
            
            return ValidationReport(
                qa_triplet_id=qa_id,
                total_claims=total_claims,
                factual_claims=factual_claims,
                hallucinated_claims=hallucinated_claims,
                neutral_claims=neutral_claims,
                overall_factuality_score=overall_score,
                claims=claims,
                passed_validation=passed,
                validation_timestamp=datetime.now().isoformat(),
                checker_type=self.config.checker_type,
                model_used=self.config.model_name
            )
            
        except Exception as e:
            logger.error(f"Error validating QA triplet {qa_triplet.get('id', 'unknown')}: {e}")
            return self._create_error_report(qa_triplet.get('id', 'unknown'), str(e))
    
    def validate_qa_batch(self, qa_triplets: List[Dict[str, Any]]) -> List[ValidationReport]:
        """
        Validate a batch of QA triplets.
        
        Args:
            qa_triplets: List of QA triplet dictionaries
            
        Returns:
            List of validation reports
        """
        reports = []
        
        # Process in batches to optimize API calls
        for i in range(0, len(qa_triplets), self.config.batch_size):
            batch = qa_triplets[i:i + self.config.batch_size]
            
            for qa_triplet in batch:
                report = self.validate_qa_triplet(qa_triplet)
                reports.append(report)
        
        return reports
    
    def filter_validated_triplets(
        self, 
        qa_triplets: List[Dict[str, Any]]
    ) -> Tuple[List[Dict[str, Any]], List[ValidationReport]]:
        """
        Filter QA triplets to keep only those that pass validation.
        
        Args:
            qa_triplets: List of QA triplet dictionaries
            
        Returns:
            Tuple of (filtered_triplets, validation_reports)
        """
        validation_reports = self.validate_qa_batch(qa_triplets)
        
        validated_triplets = []
        for qa_triplet, report in zip(qa_triplets, validation_reports):
            if report.passed_validation:
                # Add validation metadata to the triplet
                qa_triplet["validation"] = {
                    "passed": True,
                    "factuality_score": report.overall_factuality_score,
                    "hallucination_rate": report.hallucination_rate,
                    "total_claims": report.total_claims,
                    "validated_at": report.validation_timestamp
                }
                validated_triplets.append(qa_triplet)
            else:
                logger.info(
                    f"QA triplet {qa_triplet.get('id', 'unknown')} failed validation "
                    f"(factuality: {report.overall_factuality_score:.2f}, "
                    f"hallucination rate: {report.hallucination_rate:.2f})"
                )
        
        logger.info(
            f"Validation complete: {len(validated_triplets)}/{len(qa_triplets)} "
            f"triplets passed ({len(validated_triplets)/len(qa_triplets)*100:.1f}%)"
        )
        
        return validated_triplets, validation_reports
    
    def _extract_context_text(self, context: Union[str, List[Dict[str, Any]]]) -> str:
        """Extract text content from context (string or structured format)."""
        if isinstance(context, str):
            return context
        elif isinstance(context, list):
            # Extract content from structured context
            text_parts = []
            for item in context:
                if isinstance(item, dict) and "content" in item:
                    text_parts.append(item["content"])
                elif isinstance(item, str):
                    text_parts.append(item)
            return "\n".join(text_parts)
        else:
            return str(context)
    
    def _process_validation_results(self, raw_claims, validation_results) -> List[Claim]:
        """Process raw RefChecker results into structured claims."""
        claims = []
        
        for i, raw_claim in enumerate(raw_claims):
            # Get validation result for this claim
            if i < len(validation_results):
                result_label = validation_results[i]
                
                # Map RefChecker labels to our enum
                if result_label in ["SUPPORTS", "ENTAILMENT", True, 1]:
                    validation_result = ValidationResult.FACTUAL
                    confidence = 0.9
                elif result_label in ["REFUTES", "CONTRADICTION", False, 0]:
                    validation_result = ValidationResult.HALLUCINATED
                    confidence = 0.9
                else:
                    validation_result = ValidationResult.NEUTRAL
                    confidence = 0.5
            else:
                validation_result = ValidationResult.NEUTRAL
                confidence = 0.0
            
            claim = Claim(
                content=raw_claim.content if hasattr(raw_claim, 'content') else str(raw_claim),
                position=i,
                validation_result=validation_result,
                confidence_score=confidence
            )
            claims.append(claim)
        
        return claims
    
    def _create_empty_report(self, qa_id: str) -> ValidationReport:
        """Create an empty validation report."""
        return ValidationReport(
            qa_triplet_id=qa_id,
            total_claims=0,
            factual_claims=0,
            hallucinated_claims=0,
            neutral_claims=0,
            overall_factuality_score=0.0,
            claims=[],
            passed_validation=False,
            validation_timestamp=datetime.now().isoformat(),
            checker_type=self.config.checker_type
        )
    
    def _create_error_report(self, qa_id: str, error_msg: str) -> ValidationReport:
        """Create an error validation report."""
        return ValidationReport(
            qa_triplet_id=qa_id,
            total_claims=0,
            factual_claims=0,
            hallucinated_claims=0,
            neutral_claims=0,
            overall_factuality_score=0.0,
            claims=[],
            passed_validation=False,
            validation_timestamp=datetime.now().isoformat(),
            checker_type=self.config.checker_type,
            model_used=f"ERROR: {error_msg}"
        )


def create_default_config(
    checker_type: CheckerType = CheckerType.LLM_CHECKER,
    model_name: str = "gpt-4o",
    api_key: str = ""
) -> RefCheckerConfig:
    """Create a default RefChecker configuration."""
    return RefCheckerConfig(
        checker_type=checker_type,
        model_name=model_name,
        api_key=api_key,
        factuality_threshold=0.7,
        max_hallucination_rate=0.3,
        batch_size=8
    )


# Example usage and testing functions
def demo_validation():
    """Demonstrate RefChecker validation."""
    # Sample QA triplet for testing
    sample_qa = {
        "id": "test_001",
        "question": "What is the capital of France?",
        "answer": "The capital of France is Paris, which is located in the northern part of the country.",
        "context": "France is a country in Western Europe. Its capital and largest city is Paris, situated in the north-central part of the country."
    }
    
    # Configure validator (you'll need to provide your API key)
    config = create_default_config(
        checker_type=CheckerType.LLM_CHECKER,
        model_name="gpt-4o",
        api_key=os.getenv("OPENAI_API_KEY", "")
    )
    
    if not config.api_key:
        print("Please set OPENAI_API_KEY environment variable for demo")
        return
    
    try:
        validator = RefCheckerValidator(config)
        report = validator.validate_qa_triplet(sample_qa)
        
        print(f"Validation Report for QA {report.qa_triplet_id}:")
        print(f"- Total claims: {report.total_claims}")
        print(f"- Factual claims: {report.factual_claims}")
        print(f"- Hallucinated claims: {report.hallucinated_claims}")
        print(f"- Overall factuality: {report.overall_factuality_score:.2f}")
        print(f"- Passed validation: {report.passed_validation}")
        
        for i, claim in enumerate(report.claims):
            print(f"  Claim {i+1}: {claim.content[:50]}...")
            print(f"    Result: {claim.validation_result.value}")
            print(f"    Confidence: {claim.confidence_score:.2f}")
    
    except Exception as e:
        print(f"Demo failed: {e}")


if __name__ == "__main__":
    demo_validation()