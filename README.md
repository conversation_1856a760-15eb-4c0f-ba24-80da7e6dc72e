# QA Generator for Legal Documents

A comprehensive system for generating question-answer-context triplets from legal document chunks with support for multiple LLM providers (Google Gemini, OpenAI, Anthropic).

## Features

- **Multi-LLM Support**: Parametric support for Google Gemini, OpenAI GPT, and Anthropic Claude
- **Diverse Question Types**: Factual, analytical, reasoning, cross-section, comparative, inferential, and application questions
- **Multiple Difficulty Levels**: Easy, medium, hard, and very hard questions
- **Cross-Reference Questions**: Questions spanning multiple chunks and sections
- **Configurable Generation**: Customizable question distributions and parameters
- **Quality Validation**: Built-in answer validation and quality checks
- **Rich Output Format**: Comprehensive QA triplets with metadata and context

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up configuration:
```bash
cp .env.example .env
```
Edit `.env` and add your API key for your chosen LLM provider.

## Quick Start

Generate QA triplets from existing chunks:

```bash
python run_qa_generator.py
```

This will:
- Load chunks from `chunks_with_summaries.json` or `chunks_output.json`
- Generate diverse QA triplets using your configured LLM
- Save results to `qa_triplets_output.json`

## Configuration

Edit `.env` file to customize behavior:

### LLM Provider Selection
```env
LLM_PROVIDER=google          # google, openai, or anthropic
GOOGLE_API_KEY=your_key_here
OPENAI_API_KEY=your_key_here
ANTHROPIC_API_KEY=your_key_here
```

### Model Configuration
```env
GOOGLE_MODEL=gemini-2.0-flash-exp
OPENAI_MODEL=gpt-4o
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
```

### Generation Parameters
```env
QUESTIONS_PER_CHUNK=3        # Questions per chunk
ENABLE_CROSS_CHUNK=true      # Enable cross-chunk questions
MAX_CROSS_REFERENCES=5       # Max cross-reference questions
```

### Question Distribution
```env
# Difficulty levels (should sum to 1.0)
DIFFICULTY_EASY=0.3
DIFFICULTY_MEDIUM=0.4
DIFFICULTY_HARD=0.2
DIFFICULTY_VERY_HARD=0.1

# Question types (should sum to 1.0)
QUESTION_TYPE_FACTUAL=0.3
QUESTION_TYPE_ANALYTICAL=0.2
QUESTION_TYPE_REASONING=0.2
QUESTION_TYPE_CROSS_SECTION=0.1
QUESTION_TYPE_CROSS_CHUNK=0.1
QUESTION_TYPE_COMPARATIVE=0.05
QUESTION_TYPE_INFERENTIAL=0.05
QUESTION_TYPE_APPLICATION=0.0
```

## Question Types

### Factual Questions
- Direct information retrieval
- Basic definitions and terms
- Simple facts from content

### Analytical Questions  
- Deep analysis of legal implications
- Purpose and rationale behind provisions
- Evaluation of practical applications

### Reasoning Questions
- Complex legal reasoning and inference
- Application to novel scenarios
- Synthesis of multiple concepts

### Cross-Section Questions
- Connect multiple articles/sections
- Test relationships between provisions
- Require synthesis of legal rules

### Cross-Chunk Questions
- Span multiple document chunks
- Test broader legal understanding
- Show interaction between different code parts

### Comparative Questions
- Compare different legal provisions
- Analyze similarities and differences
- Evaluate relative importance

### Inferential Questions
- Make reasonable inferences
- Understand implied meanings
- Derive unstated conclusions

### Application Questions
- Present realistic legal scenarios
- Apply provisions to specific facts
- Test practical understanding

## Output Format

The system generates comprehensive QA triplets in JSON format:

```json
{
  "id": "qa_abc123def456",
  "question": "What are the fundamental principles established in Article 2 regarding the temporal application of laws?",
  "answer": "Article 2 establishes that laws only apply to future events and have no retroactive effect...",
  "context": "Art. 2. La loi ne dispose que pour l'avenir; elle n'a point d'effet rétroactif.",
  "difficulty": "medium",
  "question_type": "factual",
  "chunk_ids": ["chunk_0001"],
  "metadata": {
    "reasoning": "This question tests understanding of the non-retroactive principle...",
    "sections_referenced": "Article 2",
    "chunk_summary": "This chunk covers temporal effects of legislation...",
    "generation_model": "gemini-2.0-flash-exp"
  },
  "created_at": "2025-09-02T12:00:00.000000"
}
```

## Advanced Usage

### Command Line Interface
```bash
python qa_generator.py --chunks-file chunks.json --output qa_triplets.json --provider google --api-key YOUR_KEY
```

### Python API
```python
from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider

config = QAGenerationConfig(
    llm_provider=LLMProvider.GOOGLE,
    api_key="your_api_key",
    questions_per_chunk=5
)

generator = QAGenerator(config)
chunks = generator.load_chunks("chunks.json")
triplets = generator.generate_qa_triplets()
generator.save_qa_triplets("output.json")
```

## Testing

Run tests without API keys:

```bash
python test_qa_generator.py
```

This validates:
- Configuration and initialization
- Chunk loading functionality  
- Question generation logic (with mocked responses)
- Output format validation

## Requirements

- Python 3.8+
- API key for chosen LLM provider
- Internet connection for LLM API calls
- Input chunks file (from the main chunking system)

## Supported LLM Providers

### Google Gemini
- Models: `gemini-2.0-flash-exp`, `gemini-pro`
- Fast inference with good quality
- Cost-effective for high-volume generation

### OpenAI GPT
- Models: `gpt-4o`, `gpt-4-turbo`, `gpt-3.5-turbo`
- High-quality reasoning and analysis
- Excellent for complex legal questions

### Anthropic Claude
- Models: `claude-3-5-sonnet-20241022`, `claude-3-haiku`
- Strong analytical capabilities
- Good for nuanced legal interpretation

## Integration with Main System

The QA Generator integrates seamlessly with the main chunking system:

1. **Generate chunks**: `cd .. && python run_chunker.py`
2. **Add summaries**: `cd .. && python add_summaries.py`  
3. **Generate QA triplets**: `python run_qa_generator.py`

## License

This project is provided as-is for educational and research purposes.