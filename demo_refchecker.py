#!/usr/bin/env python3
"""
Demo: RefChecker Integration with QA Generator
==============================================

This script demonstrates how to use <PERSON><PERSON><PERSON><PERSON><PERSON> to validate generated QA triplets
and filter out hallucinated answers. It shows the complete pipeline from QA generation
to hallucination detection.

Usage:
    python demo_refchecker.py --chunks-file test_chunks_5.json --output demo_validated_qa.json --api-key YOUR_API_KEY

Features demonstrated:
- QA generation with multiple LLM providers
- RefChecker integration for hallucination detection
- Claim-level analysis and validation
- Filtering of hallucinated QA pairs
- Detailed validation reports
"""

import os
import json
import argparse
import logging
from typing import Dict, Any, List
from datetime import datetime

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import our modules
from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider, QuestionDifficulty, QuestionType
from refchecker_validator import RefCheckerValidator, RefCheckerConfig, CheckerType, ValidationResult


def create_sample_qa_triplets() -> List[Dict[str, Any]]:
    """Create sample QA triplets for testing RefChecker."""
    return [
        {
            "id": "qa_sample_001",
            "question": "What are the three categories of laws defined by Article 3 of the Civil Code?",
            "answer": "Article 3 defines three categories of laws: laws of police and safety (territorial), laws governing real estate (territorial), and laws concerning the status and capacity of persons (personal, applying to Luxembourgers abroad).",
            "context": [
                {
                    "content": "Art. 3.\\n\\nLes lois de police et de sûreté obligent tous ceux qui habitent le territoire.\\n\\nLes immeubles, même ceux possédés par des étrangers, sont régis par la loi luxembourgeoise.\\n\\nLes lois concernant l'état et la capacité des personnes régissent les Luxembourgeois, même résidant en pays étranger.",
                    "line_start": 90,
                    "line_end": 99
                }
            ]
        },
        {
            "id": "qa_sample_002",
            "question": "How many supreme courts does Luxembourg have?",
            "answer": "Luxembourg has three supreme courts: the Constitutional Court, the Supreme Court of Justice, and the Administrative Court. These courts handle constitutional matters, civil and criminal appeals, and administrative disputes respectively.",
            "context": [
                {
                    "content": "Art. 3.\\n\\nLes lois de police et de sûreté obligent tous ceux qui habitent le territoire.\\n\\nLes immeubles, même ceux possédés par des étrangers, sont régis par la loi luxembourgeoise.\\n\\nLes lois concernant l'état et la capacité des personnes régissent les Luxembourgeois, même résidant en pays étranger.",
                    "line_start": 90,
                    "line_end": 99
                }
            ]
        },
        {
            "id": "qa_sample_003",
            "question": "What does Article 3 say about real estate law?",
            "answer": "Article 3 states that real estate, even that owned by foreigners, is governed by Luxembourg law. This establishes the territorial principle for property law.",
            "context": [
                {
                    "content": "Art. 3.\\n\\nLes lois de police et de sûreté obligent tous ceux qui habitent le territoire.\\n\\nLes immeubles, même ceux possédés par des étrangers, sont régis par la loi luxembourgeoise.\\n\\nLes lois concernant l'état et la capacité des personnes régissent les Luxembourgeois, même résidant en pays étranger.",
                    "line_start": 90,
                    "line_end": 99
                }
            ]
        }
    ]


def demo_basic_refchecker(api_key: str):
    """Demonstrate basic RefChecker validation."""
    print("\\n" + "="*60)
    print("DEMO 1: Basic RefChecker Validation")
    print("="*60)
    
    # Create sample QA triplets
    qa_triplets = create_sample_qa_triplets()
    
    print(f"\\nTesting {len(qa_triplets)} sample QA triplets...")
    
    # Configure RefChecker
    config = RefCheckerConfig(
        checker_type=CheckerType.LLM_CHECKER,
        model_name="gpt-4o",
        api_key=api_key,
        factuality_threshold=0.7,
        max_hallucination_rate=0.3,
        batch_size=2
    )
    
    try:
        # Initialize validator
        validator = RefCheckerValidator(config)
        
        # Validate each triplet
        for i, qa_triplet in enumerate(qa_triplets, 1):
            print(f"\\n--- Validating QA Triplet {i} ---")
            print(f"Question: {qa_triplet['question'][:100]}...")
            print(f"Answer: {qa_triplet['answer'][:100]}...")
            
            # Validate the triplet
            report = validator.validate_qa_triplet(qa_triplet)
            
            # Display results
            print(f"\\nValidation Results:")
            print(f"  ✅ Passed: {report.passed_validation}")
            print(f"  📊 Factuality Score: {report.overall_factuality_score:.2f}")
            print(f"  🔍 Total Claims: {report.total_claims}")
            print(f"  ✓ Factual Claims: {report.factual_claims}")
            print(f"  ❌ Hallucinated Claims: {report.hallucinated_claims}")
            print(f"  ❓ Neutral Claims: {report.neutral_claims}")
            print(f"  📈 Hallucination Rate: {report.hallucination_rate:.2%}")
            
            # Show claim details
            if report.claims:
                print(f"\\n  Claim-level Analysis:")
                for j, claim in enumerate(report.claims, 1):
                    result_emoji = {
                        ValidationResult.FACTUAL: "✅",
                        ValidationResult.HALLUCINATED: "❌",
                        ValidationResult.NEUTRAL: "❓"
                    }.get(claim.validation_result, "❓")
                    
                    print(f"    {j}. {result_emoji} {claim.content[:80]}...")
                    print(f"       Result: {claim.validation_result.value}, Confidence: {claim.confidence_score:.2f}")
    
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        print("Make sure RefChecker is installed and API key is valid")


def demo_qa_generator_with_refchecker(chunks_file: str, output_file: str, api_key: str):
    """Demonstrate QA generation with RefChecker validation."""
    print("\\n" + "="*60)
    print("DEMO 2: QA Generator with RefChecker Integration")
    print("="*60)
    
    try:
        # Configure QA Generator with RefChecker enabled
        config = QAGenerationConfig(
            llm_provider=LLMProvider.GOOGLE,  # or OPENAI
            api_key=api_key,
            model_name="gemini-2.0-flash-exp",  # or "gpt-4o"
            questions_per_chunk=3,
            batch_size=5,
            
            # Enable RefChecker validation
            enable_refchecker=True,
            refchecker_model="gpt-4o",
            refchecker_api_key=api_key,  # Use same API key
            factuality_threshold=0.7,
            max_hallucination_rate=0.3,
            
            # Configure question types
            difficulty_distribution={
                QuestionDifficulty.MEDIUM: 0.5,
                QuestionDifficulty.HARD: 0.3,
                QuestionDifficulty.VERY_HARD: 0.2
            },
            question_type_distribution={
                QuestionType.FACTUAL: 0.4,
                QuestionType.ANALYTICAL: 0.3,
                QuestionType.REASONING: 0.3
            }
        )
        
        print(f"\\n📂 Loading chunks from: {chunks_file}")
        
        # Initialize QA Generator
        generator = QAGenerator(config)
        
        # Load chunks
        chunks = generator.load_chunks(chunks_file)
        print(f"✅ Loaded {len(chunks)} chunks")
        
        # Generate QA triplets (with RefChecker validation)
        print(f"\\n🤖 Generating QA triplets with RefChecker validation...")
        qa_triplets = generator.generate_qa_triplets()
        
        # Save results
        generator.save_qa_triplets(output_file)
        
        print(f"\\n📊 Generation Summary:")
        print(f"  📝 Total QA triplets generated: {len(qa_triplets)}")
        print(f"  ✅ All triplets passed RefChecker validation")
        print(f"  💾 Results saved to: {output_file}")
        
        # Show validation statistics
        validated_triplets = [t for t in qa_triplets if 'refchecker_validation' in t.metadata]
        if validated_triplets:
            avg_factuality = sum(
                t.metadata['refchecker_validation']['factuality_score'] 
                for t in validated_triplets
            ) / len(validated_triplets)
            
            avg_hallucination_rate = sum(
                t.metadata['refchecker_validation']['hallucination_rate'] 
                for t in validated_triplets
            ) / len(validated_triplets)
            
            print(f"\\n📈 Validation Statistics:")
            print(f"  📊 Average Factuality Score: {avg_factuality:.2f}")
            print(f"  🔍 Average Hallucination Rate: {avg_hallucination_rate:.2%}")
            print(f"  ✨ Quality: High (all hallucinated content filtered out)")
        
        # Show sample validated triplet
        if qa_triplets:
            sample = qa_triplets[0]
            print(f"\\n📋 Sample Validated QA Triplet:")
            print(f"  ID: {sample.id}")
            print(f"  Question: {sample.question[:100]}...")
            print(f"  Answer: {sample.answer[:100]}...")
            if 'refchecker_validation' in sample.metadata:
                val_info = sample.metadata['refchecker_validation']
                print(f"  Validation: ✅ Passed (Score: {val_info['factuality_score']:.2f})")
    
    except FileNotFoundError:
        print(f"❌ Error: Chunks file '{chunks_file}' not found")
        print("Please provide a valid chunks file or run with --create-sample")
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()


def create_sample_chunks_file(filename: str):
    """Create a sample chunks file for testing."""
    sample_chunks = {
        "chunks": [
            {
                "id": "chunk_001",
                "content": "Art. 3.\\n\\nLes lois de police et de sûreté obligent tous ceux qui habitent le territoire.\\n\\nLes immeubles, même ceux possédés par des étrangers, sont régis par la loi luxembourgeoise.\\n\\nLes lois concernant l'état et la capacité des personnes régissent les Luxembourgeois, même résidant en pays étranger.",
                "contextual_summary": "Article 3 defines three categories of laws with different territorial and personal applications.",
                "sections": [
                    {"title": "Civil Code Article 3", "content": "Laws of police and safety, real estate laws, and personal status laws"}
                ],
                "metadata": {"article": "3", "book": "Civil Code"},
                "word_count": 45,
                "character_count": 280
            },
            {
                "id": "chunk_002",
                "content": "Art. 4.\\n\\nLe juge qui refusera de juger, sous prétexte du silence, de l'obscurité ou de l'insuffisance de la loi, pourra être poursuivi comme coupable de déni de justice.",
                "contextual_summary": "Article 4 requires judges to render decisions even when the law is silent, unclear, or insufficient.",
                "sections": [
                    {"title": "Civil Code Article 4", "content": "Obligation of judges to decide cases"}
                ],
                "metadata": {"article": "4", "book": "Civil Code"},
                "word_count": 28,
                "character_count": 165
            }
        ]
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(sample_chunks, f, indent=2, ensure_ascii=False)
    
    print(f"✅ Created sample chunks file: {filename}")


def analyze_validation_report(report_file: str):
    """Analyze and display RefChecker validation report."""
    try:
        with open(report_file, 'r', encoding='utf-8') as f:
            report_data = json.load(f)
        
        print(f"\\n" + "="*60)
        print("RefChecker Validation Report Analysis")
        print("="*60)
        
        print(f"\\n📊 Overall Statistics:")
        print(f"  Total triplets validated: {report_data['total_triplets_validated']}")
        print(f"  Passed validation: {report_data['passed_validation']}")
        print(f"  Failed validation: {report_data['failed_validation']}")
        print(f"  Success rate: {report_data['passed_validation']/report_data['total_triplets_validated']*100:.1f}%")
        print(f"  Average factuality score: {report_data['average_factuality_score']:.2f}")
        
        print(f"\\n⚙️ Configuration:")
        config = report_data['config']
        print(f"  Factuality threshold: {config['factuality_threshold']}")
        print(f"  Max hallucination rate: {config['max_hallucination_rate']}")
        print(f"  RefChecker model: {config['refchecker_model']}")
        
        # Analyze detailed reports
        detailed = report_data['detailed_reports']
        if detailed:
            print(f"\\n🔍 Detailed Analysis:")
            
            # Failed validations
            failed = [r for r in detailed if not r['passed_validation']]
            if failed:
                print(f"\\n❌ Failed Validations ({len(failed)} triplets):")
                for report in failed[:3]:  # Show first 3
                    print(f"  - {report['qa_triplet_id']}: ")
                    print(f"    Factuality: {report['overall_factuality_score']:.2f}")
                    print(f"    Hallucination rate: {report['hallucination_rate']:.2%}")
                    print(f"    Claims: {report['factual_claims']}/{report['total_claims']} factual")
            
            # Top performers
            passed = [r for r in detailed if r['passed_validation']]
            if passed:
                passed_sorted = sorted(passed, key=lambda x: x['overall_factuality_score'], reverse=True)
                print(f"\\n✅ Top Performers ({len(passed)} triplets):")
                for report in passed_sorted[:3]:  # Show top 3
                    print(f"  - {report['qa_triplet_id']}: ")
                    print(f"    Factuality: {report['overall_factuality_score']:.2f}")
                    print(f"    Hallucination rate: {report['hallucination_rate']:.2%}")
                    print(f"    Claims: {report['factual_claims']}/{report['total_claims']} factual")
    
    except FileNotFoundError:
        print(f"❌ Report file '{report_file}' not found")
    except Exception as e:
        print(f"❌ Error analyzing report: {e}")


def main():
    """Main demo function."""
    parser = argparse.ArgumentParser(
        description='Demo: RefChecker Integration with QA Generator',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run basic RefChecker demo
  python demo_refchecker.py --demo basic --api-key YOUR_API_KEY
  
  # Generate and validate QA triplets
  python demo_refchecker.py --demo full --chunks-file test_chunks_5.json --output demo_qa.json --api-key YOUR_API_KEY
  
  # Create sample chunks file
  python demo_refchecker.py --create-sample sample_chunks.json
  
  # Analyze validation report
  python demo_refchecker.py --analyze-report refchecker_validation_report_20240101_120000.json
        """
    )
    
    parser.add_argument('--demo', choices=['basic', 'full'], 
                       help='Type of demo to run')
    parser.add_argument('--chunks-file', 
                       help='Input chunks JSON file')
    parser.add_argument('--output', default='demo_validated_qa.json',
                       help='Output file for validated QA triplets')
    parser.add_argument('--api-key', 
                       help='API key for LLM provider (OpenAI, Google, etc.)')
    parser.add_argument('--create-sample', 
                       help='Create a sample chunks file with given name')
    parser.add_argument('--analyze-report', 
                       help='Analyze RefChecker validation report file')
    
    args = parser.parse_args()
    
    print("🔍 RefChecker Integration Demo")
    print("===============================\\n")
    
    # Handle different operations
    if args.create_sample:
        create_sample_chunks_file(args.create_sample)
        return
    
    if args.analyze_report:
        analyze_validation_report(args.analyze_report)
        return
    
    if not args.api_key:
        # Try to get from environment
        args.api_key = os.getenv('OPENAI_API_KEY') or os.getenv('GOOGLE_API_KEY')
        if not args.api_key:
            print("❌ Error: API key required")
            print("Provide --api-key or set OPENAI_API_KEY/GOOGLE_API_KEY environment variable")
            return
    
    if args.demo == 'basic':
        demo_basic_refchecker(args.api_key)
    
    elif args.demo == 'full':
        if not args.chunks_file:
            print("❌ Error: --chunks-file required for full demo")
            print("Use --create-sample to create a test file first")
            return
        demo_qa_generator_with_refchecker(args.chunks_file, args.output, args.api_key)
    
    else:
        print("❌ Error: Specify --demo basic or --demo full")
        print("Use --help for more information")


if __name__ == '__main__':
    main()