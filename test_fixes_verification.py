#!/usr/bin/env python3
"""
Quick test to verify the language and context fixes work correctly
"""

import sys
import os
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider

def test_with_lm_studio():
    """Test QA generation with LM Studio to verify fixes."""
    
    print("🧪 Testing Language Preservation with LM Studio")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if test chunks file exists
    test_chunks_file = "test_chunks_5.json"
    if not Path(test_chunks_file).exists():
        print(f"❌ Test chunks file {test_chunks_file} not found!")
        return 1
    
    # Create configuration for LM Studio
    config = QAGenerationConfig(
        llm_provider=LLMProvider.LM_STUDIO,
        api_key='',  # Not needed for LM Studio
        model_name='cognitivecomputations/dolphin-mistral-24b-venice-edition',
        base_url='http://localhost:1234/v1',
        questions_per_chunk=1,  # 1 question per chunk for testing
        batch_size=2,  # Process 2 chunks at a time
        enable_cross_chunk=False,  # Disable cross-chunk for testing
        min_answer_length=30,
        max_answer_length=300
    )
    
    print(f"📋 Configuration:")
    print(f"   Provider: {config.llm_provider.value}")
    print(f"   Model: {config.model_name}")
    print(f"   Base URL: {config.base_url}")
    print(f"   Questions per chunk: {config.questions_per_chunk}")
    print(f"   Batch size: {config.batch_size}")
    
    try:
        # Initialize generator
        print(f"\n🚀 Initializing QA Generator...")
        generator = QAGenerator(config)
        
        # Load test chunks (just first 2 for quick test)
        print(f"📖 Loading test chunks from {test_chunks_file}...")
        all_chunks = generator.load_chunks(test_chunks_file)
        chunks = all_chunks[:2]  # Use just first 2 chunks for quick test
        print(f"   Using {len(chunks)} chunks for testing")
        
        # Show original content
        print(f"\n📊 Chunk preview:")
        for i, chunk in enumerate(chunks, 1):
            print(f"   {i}. {chunk.id}")
            print(f"      - Original content (French): {chunk.content[:100]}...")
            print(f"      - English summary: {chunk.contextual_summary[:80]}...")
        
        # Generate QA triplets
        print(f"\n🧠 Generating QA triplets with fixes...")
        triplets = generator.generate_qa_triplets()
        
        if triplets:
            print(f"\n✅ Generated {len(triplets)} QA triplets!")
            
            # Show results to verify fixes
            print(f"\n📝 Sample result to verify fixes:")
            sample = triplets[0]
            print(f"   Question: {sample.question}")
            print(f"   Answer: {sample.answer}")
            print(f"   Context (first 200 chars): {sample.context[:200]}...")
            print(f"   Context spans: {sample.context_spans}")
            
            # Verify language preservation
            is_french_question = any(word in sample.question.lower() for word in ['que', 'qui', 'quoi', 'quand', 'où', 'comment', 'pourquoi', 'est-ce', 'sont', 'selon'])
            is_french_context = 'art.' in sample.context.lower() and 'loi' in sample.context.lower()
            
            print(f"\n🔍 Verification:")
            print(f"   ✅ Question in French: {is_french_question}")
            print(f"   ✅ Context in original language: {is_french_context}")
            print(f"   ✅ Context contains original articles: {'Art.' in sample.context}")
            print(f"   ✅ Context spans present: {len(sample.context_spans) > 0}")
            
            # Save corrected results
            output_file = "test_qa_output_fixed.json"
            generator.save_qa_triplets(output_file)
            print(f"\n💾 Corrected results saved to: {output_file}")
            
        else:
            print(f"\n❌ No triplets generated!")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    # Note: This requires LM Studio to be running
    print("⚠️  Note: This test requires LM Studio to be running on localhost:1234")
    print("   If LM Studio is not available, the test will fail with connection error.")
    print("   To test with Google API instead, modify the config in the script.")
    print()
    
    sys.exit(test_with_lm_studio())