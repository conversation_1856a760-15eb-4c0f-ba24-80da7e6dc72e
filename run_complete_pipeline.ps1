# Complete QA Generation and Validation Pipeline (PowerShell)
# Run this script to execute the full workflow

Write-Host "🚀 Starting Complete QA Generation and Validation Pipeline" -ForegroundColor Cyan
Write-Host "==========================================================" -ForegroundColor Cyan

# Step 1: Generate QA Triplets
Write-Host "`n📝 Step 1: Generating QA Triplets with Evidence..." -ForegroundColor Yellow
python run_qa_generator.py --chunks-file test_chunks_5.json --output pipeline_qa_triplets.json

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ QA triplets generated successfully" -ForegroundColor Green
} else {
    Write-Host "❌ QA generation failed" -ForegroundColor Red
    exit 1
}

# Step 2: Validate with RefChecker
Write-Host "`n🔍 Step 2: Validating QA Triplets with Ref<PERSON><PERSON>cker..." -ForegroundColor Yellow
$env:OPENAI_API_KEY = "********************************************************************************************************************************************************************"
python lightweight_refchecker.py --qa-file pipeline_qa_triplets.json --model gpt-4o-mini

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ RefChecker validation completed" -ForegroundColor Green
} else {
    Write-Host "❌ RefChecker validation failed, trying simple analysis..." -ForegroundColor Yellow
    python analyze_qa_triplets.py --qa-file pipeline_qa_triplets.json
}

# Step 3: Find the latest validation report
Write-Host "`n📊 Step 3: Analyzing Validation Results..." -ForegroundColor Yellow
$LatestReport = Get-ChildItem -Path "refchecker_validation_report_*.json" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($LatestReport) {
    Write-Host "📄 Found validation report: $($LatestReport.Name)" -ForegroundColor Cyan
    python analyze_refchecker_report.py $LatestReport.Name
    Write-Host "✅ Analysis completed" -ForegroundColor Green
} else {
    Write-Host "⚠️ No validation report found, running simple analysis..." -ForegroundColor Yellow
    python analyze_qa_triplets.py --qa-file pipeline_qa_triplets.json
}

Write-Host "`n🎉 Pipeline completed successfully!" -ForegroundColor Green
Write-Host "📂 Generated files:" -ForegroundColor Cyan
Write-Host "   - QA Triplets: pipeline_qa_triplets.json" -ForegroundColor White
if ($LatestReport) {
    Write-Host "   - Validation Report: $($LatestReport.Name)" -ForegroundColor White
}
Write-Host "   - Analysis: See console output above" -ForegroundColor White