#!/usr/bin/env python3
"""
Test Suite for RefChecker Integration
====================================

Comprehensive tests for the RefChecker integration with the QA Generator system.
Tests cover validation logic, configuration, integration, and error handling.

Usage:
    python test_refchecker_integration.py
    pytest test_refchecker_integration.py -v
"""

import unittest
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
from typing import List, Dict, Any

# Import modules to test
from refchecker_validator import (
    RefCheckerValidator, RefCheckerConfig, CheckerType, ValidationResult,
    Claim, ValidationReport, create_default_config
)
from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider


class TestRefCheckerConfig(unittest.TestCase):
    """Test RefChecker configuration."""
    
    def test_default_config_creation(self):
        """Test creating default RefChecker configuration."""
        config = create_default_config()
        
        self.assertEqual(config.checker_type, CheckerType.LLM_CHECKER)
        self.assertEqual(config.model_name, "gpt-4o")
        self.assertEqual(config.factuality_threshold, 0.7)
        self.assertEqual(config.max_hallucination_rate, 0.3)
        self.assertEqual(config.batch_size, 8)
    
    def test_config_validation(self):
        """Test configuration validation."""
        # Valid config
        config = RefCheckerConfig(
            checker_type=CheckerType.LLM_CHECKER,
            api_key="test-key",
            model_name="gpt-4o"
        )
        self.assertTrue(config.validate())
        
        # Invalid config (missing API key for LLM checker)
        config = RefCheckerConfig(
            checker_type=CheckerType.LLM_CHECKER,
            api_key="",
            model_name="gpt-4o"
        )
        self.assertFalse(config.validate())
        
        # Valid config for non-LLM checker
        config = RefCheckerConfig(
            checker_type=CheckerType.ALIGNSCORE,
            api_key="",
            model_name="alignscore"
        )
        self.assertTrue(config.validate())


class TestValidationReport(unittest.TestCase):
    """Test validation report functionality."""
    
    def test_validation_report_creation(self):
        """Test creating validation report."""
        claims = [
            Claim("Test claim 1", 0, ValidationResult.FACTUAL, 0.9),
            Claim("Test claim 2", 1, ValidationResult.HALLUCINATED, 0.8),
            Claim("Test claim 3", 2, ValidationResult.NEUTRAL, 0.5)
        ]
        
        report = ValidationReport(
            qa_triplet_id="test_001",
            total_claims=3,
            factual_claims=1,
            hallucinated_claims=1,
            neutral_claims=1,
            overall_factuality_score=0.33,
            claims=claims,
            passed_validation=False,
            validation_timestamp="2024-01-01T12:00:00",
            checker_type=CheckerType.LLM_CHECKER
        )
        
        self.assertEqual(report.hallucination_rate, 1/3)
        self.assertEqual(report.factuality_rate, 1/3)
        self.assertFalse(report.passed_validation)
    
    def test_empty_report_metrics(self):
        """Test metrics for empty report."""
        report = ValidationReport(
            qa_triplet_id="test_empty",
            total_claims=0,
            factual_claims=0,
            hallucinated_claims=0,
            neutral_claims=0,
            overall_factuality_score=0.0,
            claims=[],
            passed_validation=False,
            validation_timestamp="2024-01-01T12:00:00",
            checker_type=CheckerType.LLM_CHECKER
        )
        
        self.assertEqual(report.hallucination_rate, 0.0)
        self.assertEqual(report.factuality_rate, 0.0)


class TestRefCheckerValidator(unittest.TestCase):
    """Test RefChecker validator functionality."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.sample_qa_triplet = {
            "id": "qa_test_001",
            "question": "What is the capital of France?",
            "answer": "The capital of France is Paris, which is located in the northern part of the country.",
            "context": "France is a country in Western Europe. Its capital and largest city is Paris."
        }
        
        self.config = RefCheckerConfig(
            checker_type=CheckerType.LLM_CHECKER,
            model_name="gpt-4o",
            api_key="test-key",
            factuality_threshold=0.7,
            max_hallucination_rate=0.3
        )
    
    @patch('refchecker_validator.LLMExtractor')
    @patch('refchecker_validator.LLMChecker')
    def test_validator_initialization(self, mock_checker, mock_extractor):
        """Test validator initialization."""
        # Mock the RefChecker components
        mock_extractor.return_value = Mock()
        mock_checker.return_value = Mock()
        
        validator = RefCheckerValidator(self.config)
        
        self.assertIsNotNone(validator.extractor)
        self.assertIsNotNone(validator.checker)
        mock_extractor.assert_called_once()
        mock_checker.assert_called_once()
    
    def test_context_text_extraction(self):
        """Test context text extraction from different formats."""
        with patch('refchecker_validator.LLMExtractor'), \
             patch('refchecker_validator.LLMChecker'):
            validator = RefCheckerValidator(self.config)
            
            # Test string context
            text = validator._extract_context_text("Simple text context")
            self.assertEqual(text, "Simple text context")
            
            # Test structured context
            structured_context = [
                {"content": "First part"},
                {"content": "Second part"}
            ]
            text = validator._extract_context_text(structured_context)
            self.assertEqual(text, "First part\nSecond part")
            
            # Test mixed context
            mixed_context = [
                {"content": "Structured part"},
                "Plain text part"
            ]
            text = validator._extract_context_text(mixed_context)
            self.assertEqual(text, "Structured part\nPlain text part")


class TestQAGeneratorIntegration(unittest.TestCase):
    """Test QA Generator integration with RefChecker."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
        self.chunks_file = os.path.join(self.temp_dir, "test_chunks.json")
        self.output_file = os.path.join(self.temp_dir, "test_output.json")
        
        # Create test chunks file
        chunks_data = {
            "chunks": [
                {
                    "id": "chunk_001",
                    "content": "Test legal content about French civil law.",
                    "contextual_summary": "Legal content summary",
                    "sections": [],
                    "metadata": {},
                    "word_count": 10,
                    "character_count": 50
                }
            ]
        }
        
        with open(self.chunks_file, 'w') as f:
            json.dump(chunks_data, f)
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_config_with_refchecker(self):
        """Test QA generation config with RefChecker enabled."""
        config = QAGenerationConfig(
            llm_provider=LLMProvider.GOOGLE,
            api_key="test-key",
            model_name="gemini-2.0-flash-exp",
            enable_refchecker=True,
            refchecker_model="gpt-4o",
            refchecker_api_key="refchecker-key",
            factuality_threshold=0.8,
            max_hallucination_rate=0.2
        )
        
        self.assertTrue(config.enable_refchecker)
        self.assertEqual(config.refchecker_model, "gpt-4o")
        self.assertEqual(config.factuality_threshold, 0.8)
        self.assertEqual(config.max_hallucination_rate, 0.2)


class TestErrorHandling(unittest.TestCase):
    """Test error handling in RefChecker integration."""
    
    def test_missing_refchecker_import(self):
        """Test handling of missing RefChecker package."""
        with patch('builtins.__import__', side_effect=ImportError("No module named 'refchecker'")):
            with self.assertRaises(ImportError) as cm:
                config = RefCheckerConfig(api_key="test-key")
                RefCheckerValidator(config)
            
            self.assertIn("RefChecker not installed", str(cm.exception))
    
    def test_invalid_api_key(self):
        """Test handling of invalid API key."""
        config = RefCheckerConfig(
            checker_type=CheckerType.LLM_CHECKER,
            api_key="",  # Empty API key
            model_name="gpt-4o"
        )
        
        with self.assertRaises(ValueError):
            RefCheckerValidator(config)


class TestEndToEnd(unittest.TestCase):
    """End-to-end integration tests."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """Clean up test fixtures."""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_complete_pipeline_simulation(self):
        """Test complete pipeline from chunks to validated QA triplets."""
        # This test simulates the complete pipeline without actual API calls
        
        # Create test data
        chunks_file = os.path.join(self.temp_dir, "chunks.json")
        output_file = os.path.join(self.temp_dir, "output.json")
        
        chunks_data = {
            "chunks": [
                {
                    "id": "chunk_test",
                    "content": "Article 1 states that all citizens are equal before the law.",
                    "contextual_summary": "Legal equality principle",
                    "sections": [],
                    "metadata": {"article": "1"},
                    "word_count": 12,
                    "character_count": 60
                }
            ]
        }
        
        with open(chunks_file, 'w') as f:
            json.dump(chunks_data, f)
        
        # Test configuration creation
        config = QAGenerationConfig(
            llm_provider=LLMProvider.GOOGLE,
            api_key="test-key",
            enable_refchecker=True,
            refchecker_model="gpt-4o",
            factuality_threshold=0.8
        )
        
        # Verify configuration
        self.assertTrue(config.enable_refchecker)
        self.assertEqual(config.factuality_threshold, 0.8)
        
        # Test that the pipeline would work (without actual API calls)
        # This verifies the structure and configuration are correct
        with patch('qa_generator.GoogleGeminiProvider') as mock_provider_class:
            mock_provider = Mock()
            mock_provider.validate_configuration.return_value = True
            mock_provider_class.return_value = mock_provider
            
            generator = QAGenerator(config)
            chunks = generator.load_chunks(chunks_file)
            
            self.assertEqual(len(chunks), 1)
            self.assertEqual(chunks[0].id, "chunk_test")


if __name__ == '__main__':
    # Set up test suite
    suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestRefCheckerConfig,
        TestValidationReport,
        TestRefCheckerValidator,
        TestQAGeneratorIntegration,
        TestErrorHandling,
        TestEndToEnd
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n{'='*60}")
    print("RefChecker Integration Test Summary")
    print(f"{'='*60}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.testsRun > 0:
        success_rate = (result.testsRun - len(result.failures) - len(result.errors))/result.testsRun*100
        print(f"Success rate: {success_rate:.1f}%")
    
    if result.failures:
        print(f"\n❌ FAILURES:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n💥 ERRORS:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback.split('Exception:')[-1].strip()}")
    
    if not result.failures and not result.errors:
        print("\n✅ All tests passed! RefChecker integration is working correctly.")
    
    print(f"\n📋 Next steps:")
    print(f"  1. Install RefChecker: pip install refchecker")
    print(f"  2. Download spaCy model: python -m spacy download en_core_web_sm")
    print(f"  3. Set up API keys for your LLM provider")
    print(f"  4. Run the demo: python demo_refchecker.py --demo basic --api-key YOUR_KEY")