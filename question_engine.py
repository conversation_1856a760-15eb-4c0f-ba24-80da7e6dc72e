"""
Question Generation Engine for Legal Documents
===============================================

Implements different types and difficulty levels of questions for legal document chunks.
Supports factual, analytical, reasoning, cross-section, and comparative questions.
"""

import json
import random
from typing import List, Dict, Any, Optional, Tuple
from enum import Enum
import uuid
from datetime import datetime

from qa_generator import (
    QATriplet, ChunkData, QuestionDifficulty, QuestionType, 
    BaseLLMProvider, QAGenerationConfig
)


class PromptTemplates:
    """Templates for different types of question generation prompts."""
    
    FACTUAL_EASY = """
Générez une question factuelle simple sur ce contenu juridique du Code Civil Luxembourgeois. 
POSSIBLE EN FRANÇAIS OU LUXEMBOURGEOIS - conservez la langue originale du texte.

Contenu: {content}
Résumé: {summary}

Créez une question qui demande:
- Définitions ou termes de base
- Faits simples énoncés dans le contenu  
- Questions qui, quoi, quand, où
- Récupération directe d'informations

Formatez votre réponse en JSON:
{{
    "question": "Votre question ici (dans la langue originale du texte)",
    "answer": "Réponse directe du contenu (dans la langue originale)", 
    "reasoning": "Brève explication de pourquoi cette réponse est correcte",
    "evidence": ["phrase exacte du contexte 1", "phrase exacte du contexte 2"]
}}

La question doit être claire et pouvoir être répondue directement à partir du contenu fourni.
IMPORTANT: Conservez la langue originale (français/luxembourgeois) dans vos questions et réponses.
Evidence: Incluez 2-3 phrases exactes du contexte qui supportent directement votre réponse.
"""
    
    FACTUAL_MEDIUM = """
Générez une question factuelle de difficulté moyenne sur ce contenu juridique du Code Civil Luxembourgeois.
RÉPONDEZ EN FRANÇAIS OU LUXEMBOURGEOIS - conservez la langue originale du texte.

Contenu: {content}
Résumé: {summary}

Créez une question qui nécessite:
- Compréhension des concepts juridiques mentionnés
- Connexion de plusieurs faits du contenu
- Interprétation de base du langage juridique  
- Compréhension des relations entre les dispositions

Formatez votre réponse en JSON:
{{
    "question": "Votre question ici (dans la langue originale)",
    "answer": "Réponse nécessitant une certaine interprétation (dans la langue originale)",
    "reasoning": "Explication du concept juridique et de la dérivation de la réponse",
    "evidence": ["phrase exacte du contexte 1", "phrase exacte du contexte 2"]
}}

La question doit tester la compréhension, pas seulement la mémorisation.
IMPORTANT: Conservez la langue originale (français/luxembourgeois) dans vos questions et réponses.
Evidence: Incluez 2-3 phrases exactes du contexte qui supportent directement votre réponse.
"""
    
    ANALYTICAL_HARD = """
Générez une question analytique difficile sur ce contenu juridique du Code Civil Luxembourgeois.
RÉPONDEZ EN FRANÇAIS OU LUXEMBOURGEOIS - conservez la langue originale du texte.

Contenu: {content}
Résumé: {summary}

Créez une question qui nécessite:
- Analyse approfondie des implications juridiques
- Compréhension du but et de la logique derrière les dispositions
- Analyse des conflits ou exceptions potentiels
- Évaluation des applications pratiques

Formatez votre réponse en JSON:
{{
    "question": "Votre question analytique ici (dans la langue originale)",
    "answer": "Réponse basée sur une analyse complète (dans la langue originale)",
    "reasoning": "Explication détaillée de l'approche analytique et du raisonnement juridique",
    "evidence": ["phrase exacte du contexte 1", "phrase exacte du contexte 2"]
}}

Concentrez-vous sur "pourquoi" et "comment" plutôt que sur "quoi".
IMPORTANT: Conservez la langue originale (français/luxembourgeois) dans vos questions et réponses.
Evidence: Incluez 2-3 phrases exactes du contexte qui supportent directement votre analyse.
"""
    
    REASONING_VERY_HARD = """
Générez une question de raisonnement très difficile sur ce contenu juridique du Code Civil Luxembourgeois.
RÉPONDEZ EN FRANÇAIS OU LUXEMBOURGEOIS - conservez la langue originale du texte.

Contenu: {content}
Résumé: {summary}

Créez une question qui nécessite:
- Raisonnement juridique complexe et inférence
- Application de principes à des scénarios nouveaux
- Synthèse de plusieurs concepts juridiques
- Évaluation critique de cas limites ou de limitations

Formatez votre réponse en JSON:
{{
    "question": "Votre question de raisonnement complexe ici (dans la langue originale)",
    "answer": "Réponse nécessitant un raisonnement juridique sophistiqué (dans la langue originale)",
    "reasoning": "Explication détaillée du processus de raisonnement et des principes juridiques appliqués",
    "evidence": ["phrase exacte du contexte 1", "phrase exacte du contexte 2"]
}}

La question doit défier même les professionnels juridiques expérimentés.
IMPORTANT: Conservez la langue originale (français/luxembourgeois) dans vos questions et réponses.
Evidence: Incluez 2-3 phrases exactes du contexte qui supportent directement votre raisonnement.
"""
    
    CROSS_SECTION = """
Générez une question qui nécessite la compréhension de plusieurs sections dans ce chapitre.
RÉPONDEZ EN FRANÇAIS OU LUXEMBOURGEOIS - conservez la langue originale du texte.

Contenu: {content}
Résumé: {summary}
Sections incluses: {sections}

Créez une question qui:
- Connecte les informations à travers différents articles/sections
- Teste la compréhension des relations entre les dispositions
- Nécessite la synthèse de plusieurs règles juridiques
- Montre comment les sections travaillent ensemble

Formatez votre réponse en JSON:
{{
    "question": "Votre question trans-section ici (dans la langue originale)",
    "answer": "Réponse qui puise dans plusieurs sections (dans la langue originale)",
    "reasoning": "Explication de comment les sections sont liées et soutiennent la réponse",
    "evidence": ["phrase exacte du contexte 1", "phrase exacte du contexte 2"]
}}

Indiquez clairement quelles sections sont référencées dans votre réponse.
IMPORTANT: Conservez la langue originale (français/luxembourgeois) dans vos questions et réponses.
Evidence: Incluez 2-3 phrases exactes du contexte qui supportent directement votre réponse.
"""
    
    COMPARATIVE = """
Generate a comparative question about this legal content.

Content: {content}
Summary: {summary}

Create a question that:
- Compares different legal provisions or concepts
- Analyzes similarities and differences
- Evaluates relative importance or hierarchy
- Contrasts different scenarios or applications

Format your response as JSON:
{{
    "question": "Your comparative question here",
    "answer": "Answer highlighting comparisons and contrasts",
    "reasoning": "Explanation of the comparative analysis and legal distinctions"
}}

Focus on legal distinctions and comparative analysis.
"""
    
    INFERENTIAL = """
Generate an inferential question about this legal content.

Content: {content}
Summary: {summary}

Create a question that requires:
- Making reasonable inferences from stated provisions
- Understanding implied meanings and consequences
- Deriving unstated conclusions from legal principles
- Reading between the lines of legal text

Format your response as JSON:
{{
    "question": "Your inferential question here",
    "answer": "Answer based on logical inference from the content",
    "reasoning": "Explanation of the inference process and supporting evidence"
}}

The answer should be logically derivable but not explicitly stated.
"""
    
    APPLICATION = """
Generate a practical application question about this legal content.

Content: {content}
Summary: {summary}

Create a question that:
- Presents a realistic legal scenario
- Requires application of the provisions to specific facts
- Tests practical understanding of legal rules
- Shows how the law works in practice

Format your response as JSON:
{{
    "question": "Your application question with a realistic scenario",
    "answer": "Answer showing how the law applies to the scenario",
    "reasoning": "Explanation of the legal application and relevant provisions"
}}

Include a concrete, realistic scenario in your question.
"""
    
    BATCH_CHUNKS = """
Generate diverse question-answer-context triplets from multiple legal document chunks from the Luxembourg Civil Code.

You will receive {batch_size} chunks with their sections and contextual summaries. Generate {questions_per_chunk} questions per chunk with varied difficulty levels and question types.

Chunks to process:
{chunks_data}

Generate questions with these requirements:
- Question types: factual, analytical, reasoning, cross-section, comparative, inferential, application
- Difficulty levels: easy, medium, hard, very_hard  
- Include cross-references between chunks when appropriate
- Ensure legal accuracy and professional language

For each question, provide:
1. The question text (ending with ?)
2. A comprehensive answer (50-500 characters)
3. Brief reasoning for the answer
4. Relevant chunk IDs

Format as JSON array:
[
  {{
    "question": "Your question here?",
    "answer": "Comprehensive answer with legal analysis",
    "reasoning": "Explanation of legal reasoning",
    "difficulty": "medium",
    "question_type": "analytical", 
    "chunk_ids": ["chunk_0001"]
  }}
]

Generate {total_questions} questions total across all chunks.
"""
    
    CROSS_CHUNK = """
Generate a question that requires understanding content from multiple chunks.

Chunk 1 Content: {chunk1_content}
Chunk 1 Summary: {chunk1_summary}

Chunk 2 Content: {chunk2_content}
Chunk 2 Summary: {chunk2_summary}

Create a question that:
- Requires information from both chunks
- Tests understanding of relationships between different parts of the code
- Shows how different legal provisions interact
- Requires synthesis across broader legal context

Format your response as JSON:
{{
    "question": "Your cross-chunk question here",
    "answer": "Answer drawing from both chunks",
    "reasoning": "Explanation of how the chunks relate and support the answer",
    "evidence": ["phrase from chunk 1", "phrase from chunk 2"],
    "chunk_references": ["chunk_id_1", "chunk_id_2"]
}}

Clearly indicate which chunks provide support for your answer.
Evidence: Include 2-3 exact phrases from both chunks that support your answer.
"""


class QuestionGenerator:
    """Generates questions of different types and difficulties."""
    
    def __init__(self, llm_provider: BaseLLMProvider, config: QAGenerationConfig):
        self.llm_provider = llm_provider
        self.config = config
        self.templates = PromptTemplates()
    
    def generate_batch_questions(self, chunks: List[ChunkData], batch_size: int = 6) -> List[QATriplet]:
        """Generate questions for a batch of chunks in a single LLM call."""
        
        try:
            print(f"📝 Generating {len(chunks)} * {self.config.questions_per_chunk} = {len(chunks) * self.config.questions_per_chunk} questions in batch...")
            
            # Reduce batch size if too many chunks to fit in context
            effective_batch_size = min(len(chunks), self.config.batch_size)  # Use configured batch size
            chunks = chunks[:effective_batch_size]
            
            # Pre-select difficulty and question types for each question using weighted selection
            question_specs = []
            for chunk in chunks:
                for _ in range(self.config.questions_per_chunk):
                    # Use weighted selection functions to ensure proper distribution
                    difficulty = self._select_weighted_difficulty()
                    question_type = self._select_weighted_question_type()
                    
                    question_specs.append({
                        'difficulty': difficulty.value,
                        'type': question_type.value, 
                        'chunk_id': chunk.id
                    })
            
            print(f"🎯 Pre-selected question distribution:")
            diff_counts = {}
            type_counts = {}
            for spec in question_specs:
                diff_counts[spec['difficulty']] = diff_counts.get(spec['difficulty'], 0) + 1
                type_counts[spec['type']] = type_counts.get(spec['type'], 0) + 1
            print(f"   Difficulties: {diff_counts}")
            print(f"   Types: {type_counts}")
            
            # Format chunks data for the prompt with minimal truncation to preserve context
            chunks_data_parts = []
            for i, chunk in enumerate(chunks, 1):
                sections_info = self._format_sections_info_with_spans([dict(s) for s in chunk.sections])[:300]  # Increased sections info
                # Reduce content truncation to preserve full context for questions
                content_preview = chunk.content[:1500] + "..." if len(chunk.content) > 1500 else chunk.content
                summary_preview = chunk.contextual_summary[:400] + "..." if len(chunk.contextual_summary) > 400 else chunk.contextual_summary
                
                chunk_info = f"""Chunk {i} (ID: {chunk.id}):
Sections: {sections_info}
Content: {content_preview}
"""
                chunks_data_parts.append(chunk_info)
            
            chunks_data = "\n".join(chunks_data_parts)
            total_questions = len(question_specs)
            
            # Use enhanced prompt template with pre-selected specifications
            prompt = self._create_simplified_batch_prompt(chunks_data, len(chunks), total_questions, question_specs)
            
            # Generate response from LLM
            response = self.llm_provider.generate_content(prompt)
            
            # Parse JSON response
            questions_data = self._parse_batch_response(response)
            
            if not questions_data:
                print(f"⚠️ No valid questions generated for batch of {len(chunks)} chunks")
                return []
            
            # Convert to QATriplet objects
            triplets = []
            for qa_data in questions_data:
                try:
                    # Find corresponding chunk(s)
                    chunk_ids = qa_data.get('chunk_ids', [chunks[0].id])  # Fallback to first chunk
                    relevant_chunks = [c for c in chunks if c.id in chunk_ids]
                    
                    if not relevant_chunks:
                        relevant_chunks = [chunks[0]]  # Fallback
                    
                    # Create context as list of content objects with line spans
                    context_parts = []
                    span_references = []
                    for chunk in relevant_chunks:
                        # Extract sections with their content and line information
                        for section in chunk.sections:
                            content_obj = {
                                "content": section.get('content', ''),
                                "line_start": section.get('line_start', 0),
                                "line_end": section.get('line_end', 0)
                            }
                            context_parts.append(content_obj)
                            
                            # Extract span references
                            if 'context_span' in section:
                                span_id = section['context_span'].get('span_id', '')
                                if span_id:
                                    span_references.append(span_id)
                            elif section.get('line_start') and section.get('line_end'):
                                span_references.append(f"span_{section['line_start']}_{section['line_end']}")
                    
                    # Use the list of content objects as context
                    context = context_parts
                    
                    # Parse context spans from QA data or extract from chunks
                    context_spans = qa_data.get('context_spans', span_references[:3])  # Limit to 3 spans
                    
                    # Parse difficulty and question type
                    try:
                        difficulty = QuestionDifficulty(qa_data.get('difficulty', 'medium'))
                    except ValueError:
                        difficulty = QuestionDifficulty.MEDIUM
                    
                    try:
                        question_type = QuestionType(qa_data.get('question_type', 'factual'))
                    except ValueError:
                        question_type = QuestionType.FACTUAL
                    
                    triplet = QATriplet(
                        id=f"qa_{uuid.uuid4().hex[:12]}",
                        question=qa_data['question'],
                        answer=qa_data['answer'],
                        context=context,
                        difficulty=difficulty,
                        question_type=question_type,
                        chunk_ids=chunk_ids,
                        context_spans=context_spans,
                        evidence=qa_data.get('evidence', []),  # Extract evidence from LLM response
                        metadata={
                            'reasoning': qa_data.get('reasoning', ''),
                            'generation_model': self.llm_provider.model_name,
                            'batch_generated': True,
                            'batch_size': len(chunks),
                            'context_optimized': True,
                            'context_spans': context_spans,
                            'span_count': len(context_spans)
                        },
                        created_at=datetime.now().isoformat()
                    )
                    
                    # Validate the triplet
                    if self._validate_triplet(triplet):
                        triplets.append(triplet)
                    
                except Exception as e:
                    print(f"⚠️ Error processing batch question: {str(e)}")
                    continue
            
            print(f"✅ Generated {len(triplets)} valid questions from batch of {len(chunks)} chunks")
            return triplets
            
        except Exception as e:
            print(f"❌ Error in batch question generation: {str(e)}")
            return []
    
    def _select_weighted_difficulty(self) -> QuestionDifficulty:
        """Select difficulty based on configured weights."""
        import random
        difficulties = list(self.config.difficulty_distribution.keys())
        weights = list(self.config.difficulty_distribution.values())
        
        return random.choices(difficulties, weights=weights)[0]
    
    def _select_weighted_question_type(self) -> QuestionType:
        """Select question type based on configured weights."""
        import random
        # Filter out cross-chunk type for single chunk questions
        filtered_distribution = {
            k: v for k, v in self.config.question_type_distribution.items() 
            if k != QuestionType.CROSS_CHUNK
        }
        
        question_types = list(filtered_distribution.keys())
        weights = list(filtered_distribution.values())
        
        return random.choices(question_types, weights=weights)[0]
    
    def _format_sections_info_with_spans(self, sections: List[Dict[str, Any]]) -> str:
        """Format sections information with context spans for prompts."""
        if not sections:
            return "No specific sections available"
        
        sections_list = []
        for section in sections:
            section_info = f"{section.get('level', 'section').title()} {section.get('number', 'N/A')}"
            if section.get('title'):
                section_info += f": {section['title']}"
            
            # Add span information if available
            if 'context_span' in section:
                span = section['context_span']
                span_id = span.get('span_id', f"span_{span.get('start_line', 0)}_{span.get('end_line', 0)}")
                section_info += f" (span: {span_id})"
            
            sections_list.append(section_info)
        
        return "; ".join(sections_list)
    
    def _parse_batch_response(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """Parse JSON array response from batch generation."""
        try:
            # Find JSON array in the response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                questions_list = json.loads(json_str)
                
                if isinstance(questions_list, list):
                    return questions_list
                else:
                    print(f"⚠️ Expected JSON array, got: {type(questions_list)}")
                    return None
            else:
                print(f"⚠️ Could not find JSON array in batch response: {response[:200]}...")
                return None
                
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON decode error in batch response: {str(e)}")
            return None
    
    def _validate_triplet(self, triplet: QATriplet) -> bool:
        """Validate a QA triplet meets quality requirements."""
        
        # Check minimum lengths
        if len(triplet.question) < 20:
            return False
        
        if len(triplet.answer) < self.config.min_answer_length:
            return False
        
        if len(triplet.answer) > self.config.max_answer_length:
            return False
        
        # Check for question marks in questions
        if not triplet.question.strip().endswith('?'):
            return False
        
        # Check that answer is not just a repetition of the question
        if triplet.answer.lower() in triplet.question.lower():
            return False
        
        # Additional quality checks could be added here
        
        return True
    
    def _create_simplified_batch_prompt(self, chunks_data: str, batch_size: int, total_questions: int, question_specs: List[Dict]) -> str:
        """Create a simplified batch prompt with pre-selected difficulty and types."""
        
        # Create question specifications table
        specs_info = "\n".join([
            f"Question {i+1}: Difficulté={spec['difficulty']}, Type={spec['type']}, Chunk={spec['chunk_id']}"
            for i, spec in enumerate(question_specs)
        ])
        
        return f"""Générez {total_questions} questions juridiques selon les spécifications suivantes:

{specs_info}

Chapitres du Code Civil Luxembourgeois:
{chunks_data}

Exigences:
- RESPECTEZ EXACTEMENT la difficulté et le type spécifiés pour chaque question
- Répondez en français/luxembourgeois - conservez la langue originale
- Questions se terminent par ?
- Réponses 50-300 caractères
- OBLIGATOIRE: Incluez 2-3 phrases exactes du contexte comme preuves

Format JSON:
[
  {{
    "question": "Votre question? (dans la langue originale)",
    "answer": "Réponse juridique avec références de contexte (dans la langue originale)",
    "reasoning": "Brève explication",
    "difficulty": "[UTILISEZ LA DIFFICULTÉ SPÉCIFIÉE]",
    "question_type": "[UTILISEZ LE TYPE SPÉCIFIÉ]",
    "chunk_ids": ["chunk_0001"],
    "context_spans": ["span_84_89", "span_90_99"],
    "evidence": ["phrase exacte du contexte 1", "phrase exacte du contexte 2"]
  }}
]

IMPORTANT: Respectez EXACTEMENT les difficultés et types spécifiés ci-dessus pour chaque question.
Générez {total_questions} questions dans l'ordre spécifié:"""
    
    def _estimate_tokens(self, text: str) -> int:
        """Rough token estimation (1 token ≈ 4 characters)."""
        return len(text) // 4
    
    def _parse_batch_response(self, response: str) -> Optional[List[Dict[str, Any]]]:
        """Parse JSON array response from batch generation."""
        try:
            # Find JSON array in the response
            json_start = response.find('[')
            json_end = response.rfind(']') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                questions_list = json.loads(json_str)
                
                if isinstance(questions_list, list):
                    return questions_list
                else:
                    print(f"⚠️ Expected JSON array, got: {type(questions_list)}")
                    return None
            else:
                print(f"⚠️ Could not find JSON array in batch response: {response[:200]}...")
                return None
                
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON decode error in batch response: {str(e)}")
            return None
    
    def generate_question(self, chunk: ChunkData, difficulty: QuestionDifficulty, 
                         question_type: QuestionType) -> Optional[QATriplet]:
        """Generate a single question for a chunk."""
        
        try:
            # Select appropriate prompt template
            prompt = self._get_prompt_template(difficulty, question_type)
            
            # Prepare content for prompt - keep more content for better context
            content_full = chunk.content  # Use full content instead of truncated
            sections_info = self._format_sections_info(chunk.sections)
            
            # Format prompt with chunk data
            formatted_prompt = prompt.format(
                content=content_full,
                summary=chunk.contextual_summary,
                sections=sections_info
            )
            
            # Generate response from LLM
            response = self.llm_provider.generate_content(formatted_prompt)
            
            # Parse JSON response
            qa_data = self._parse_llm_response(response)
            
            if qa_data:
                # Create QA triplet with context as list of content objects
                context_spans = self._extract_context_spans(chunk.sections)
                
                # Create context as list of section content objects with line spans
                context_list = []
                for section in chunk.sections:
                    content_obj = {
                        "content": section.get('content', ''),
                        "line_start": section.get('line_start', 0),
                        "line_end": section.get('line_end', 0)
                    }
                    context_list.append(content_obj)
                
                triplet = QATriplet(
                    id=f"qa_{uuid.uuid4().hex[:12]}",
                    question=qa_data['question'],
                    answer=qa_data['answer'],
                    context=context_list,  # Use list of content objects
                    difficulty=difficulty,
                    question_type=question_type,
                    chunk_ids=[chunk.id],
                    context_spans=context_spans,
                    evidence=qa_data.get('evidence', []),  # Extract evidence from LLM response
                    metadata={
                        'reasoning': qa_data.get('reasoning', ''),
                        'sections_referenced': sections_info,
                        'chunk_summary': chunk.contextual_summary,
                        'generation_model': self.llm_provider.model_name,
                        'context_spans': context_spans,
                        'span_count': len(context_spans)
                    },
                    created_at=datetime.now().isoformat()
                )
                
                # Validate the triplet
                if self._validate_triplet(triplet):
                    return triplet
                else:
                    print(f"⚠️ Generated triplet failed validation for {difficulty.value} {question_type.value}")
            
        except Exception as e:
            print(f"❌ Error generating question: {str(e)}")
        
        return None
    
    def generate_cross_chunk_question(self, chunk1: ChunkData, chunk2: ChunkData,
                                    difficulty: QuestionDifficulty) -> Optional[QATriplet]:
        """Generate a question that spans multiple chunks."""
        
        try:
            prompt = self.templates.CROSS_CHUNK.format(
                chunk1_content=chunk1.content[:1000] + "..." if len(chunk1.content) > 1000 else chunk1.content,
                chunk1_summary=chunk1.contextual_summary,
                chunk2_content=chunk2.content[:1000] + "..." if len(chunk2.content) > 1000 else chunk2.content,
                chunk2_summary=chunk2.contextual_summary
            )
            
            response = self.llm_provider.generate_content(prompt)
            qa_data = self._parse_llm_response(response)
            
            if qa_data:
                # Combine contexts as list of content objects from both chunks
                combined_context = []
                for chunk in [chunk1, chunk2]:
                    for section in chunk.sections:
                        content_obj = {
                            "content": section.get('content', ''),
                            "line_start": section.get('line_start', 0),
                            "line_end": section.get('line_end', 0)
                        }
                        combined_context.append(content_obj)
                
                triplet = QATriplet(
                    id=f"qa_{uuid.uuid4().hex[:12]}",
                    question=qa_data['question'],
                    answer=qa_data['answer'],
                    context=combined_context,
                    difficulty=difficulty,
                    question_type=QuestionType.CROSS_CHUNK,
                    chunk_ids=[chunk1.id, chunk2.id],
                    evidence=qa_data.get('evidence', []),  # Extract evidence from LLM response
                    metadata={
                        'reasoning': qa_data.get('reasoning', ''),
                        'chunk_references': qa_data.get('chunk_references', []),
                        'generation_model': self.llm_provider.model_name,
                        'cross_chunk_analysis': True
                    },
                    created_at=datetime.now().isoformat()
                )
                
                if self._validate_triplet(triplet):
                    return triplet
            
        except Exception as e:
            print(f"❌ Error generating cross-chunk question: {str(e)}")
        
        return None
    
    def _get_prompt_template(self, difficulty: QuestionDifficulty, question_type: QuestionType) -> str:
        """Get appropriate prompt template based on difficulty and type."""
        
        if question_type == QuestionType.FACTUAL:
            if difficulty == QuestionDifficulty.EASY:
                return self.templates.FACTUAL_EASY
            elif difficulty in [QuestionDifficulty.MEDIUM, QuestionDifficulty.HARD]:
                return self.templates.FACTUAL_MEDIUM
            else:  # VERY_HARD
                return self.templates.ANALYTICAL_HARD
        
        elif question_type == QuestionType.ANALYTICAL:
            return self.templates.ANALYTICAL_HARD
        
        elif question_type == QuestionType.REASONING:
            return self.templates.REASONING_VERY_HARD
        
        elif question_type == QuestionType.CROSS_SECTION:
            return self.templates.CROSS_SECTION
        
        elif question_type == QuestionType.COMPARATIVE:
            return self.templates.COMPARATIVE
        
        elif question_type == QuestionType.INFERENTIAL:
            return self.templates.INFERENTIAL
        
        elif question_type == QuestionType.APPLICATION:
            return self.templates.APPLICATION
        
        else:
            return self.templates.FACTUAL_MEDIUM  # Default fallback
    
    def _format_sections_info(self, sections: List[Dict[str, Any]]) -> str:
        """Format sections information for prompts."""
        if not sections:
            return "No specific sections available"
        
        sections_list = []
        for section in sections:
            section_info = f"{section.get('level', 'section').title()} {section.get('number', 'N/A')}"
            if section.get('title'):
                section_info += f": {section['title']}"
            sections_list.append(section_info)
        
        return "; ".join(sections_list)
    
    def _extract_context_spans(self, sections: List[Dict[str, Any]]) -> List[str]:
        """Extract context span information from sections."""
        spans = []
        for section in sections:
            if 'context_span' in section:
                span_info = section['context_span']
                start_line = span_info.get('start_line', 0)
                end_line = span_info.get('end_line', 0)
                spans.append(f"span_{start_line}_{end_line}")
            elif 'line_start' in section and 'line_end' in section:
                # Fallback to direct line information
                spans.append(f"span_{section['line_start']}_{section['line_end']}")
        return spans

    def _format_sections_info_with_spans(self, sections: List[Dict[str, Any]]) -> str:
        """Format sections information with context spans for prompts."""
        if not sections:
            return "No specific sections available"
        
        sections_list = []
        for section in sections:
            section_info = f"{section.get('level', 'section').title()} {section.get('number', 'N/A')}"
            if section.get('title'):
                section_info += f": {section['title']}"
            
            # Add span information if available
            if 'context_span' in section:
                span = section['context_span']
                span_id = span.get('span_id', f"span_{span.get('start_line', 0)}_{span.get('end_line', 0)}")
                section_info += f" (span: {span_id})"
            
            sections_list.append(section_info)
        
        return "; ".join(sections_list)
    
    def _parse_llm_response(self, response: str) -> Optional[Dict[str, Any]]:
        """Parse JSON response from LLM."""
        try:
            # Find JSON in the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            
            if json_start != -1 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                print(f"⚠️ Could not find JSON in LLM response: {response[:100]}...")
                return None
                
        except json.JSONDecodeError as e:
            print(f"⚠️ JSON decode error: {str(e)}")
            return None
    
    def _validate_triplet(self, triplet: QATriplet) -> bool:
        """Validate a QA triplet meets quality requirements."""
        
        # Check minimum lengths
        if len(triplet.question) < 20:
            return False
        
        if len(triplet.answer) < self.config.min_answer_length:
            return False
        
        if len(triplet.answer) > self.config.max_answer_length:
            return False
        
        # Check for question marks in questions
        if not triplet.question.strip().endswith('?'):
            return False
        
        # Check that answer is not just a repetition of the question
        if triplet.answer.lower() in triplet.question.lower():
            return False
        
        # Additional quality checks could be added here
        
        return True


class QABatchGenerator:
    """Generates batches of QA triplets with progress tracking."""
    
    def __init__(self, question_generator: QuestionGenerator, config: QAGenerationConfig):
        self.question_generator = question_generator
        self.config = config
    
    def generate_batch_for_chunks(self, chunks: List[ChunkData]) -> List[QATriplet]:
        """Generate QA triplets for a batch of chunks using efficient batch processing."""
        
        all_triplets = []
        total_chunks = len(chunks)
        # Reduce batch size for 4K context limit
        batch_size = min(self.config.batch_size, 3)  # Max 3 chunks for LM Studio 4K context
        
        print(f"🎯 Generating QA triplets for {total_chunks} chunks using optimized batch processing (batch size: {batch_size})...")
        
        # Process chunks in batches
        for i in range(0, total_chunks, batch_size):
            batch_chunks = chunks[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (total_chunks + batch_size - 1) // batch_size
            
            print(f"\r📚 Processing batch {batch_num}/{total_batches} (chunks {i+1}-{min(i+batch_size, total_chunks)})...", end='', flush=True)
            
            # Generate questions for this batch using LLM batch processing
            batch_triplets = self.question_generator.generate_batch_questions(batch_chunks, len(batch_chunks))
            all_triplets.extend(batch_triplets)
            
            # Small delay to avoid overwhelming the local LLM
            import time
            time.sleep(1.0)  # Slightly longer delay for stability
        
        print(f"\n✅ Generated {len(all_triplets)} questions from {total_chunks} chunks using optimized batch processing")
        
        # Generate cross-chunk questions if enabled (reduced number for efficiency)
        if self.config.enable_cross_chunk and len(chunks) > 1:
            print("🔗 Generating cross-chunk questions...")
            cross_triplets = self._generate_cross_chunk_questions_batch(chunks)
            all_triplets.extend(cross_triplets)
            print(f"✅ Generated {len(cross_triplets)} cross-chunk questions")
        
        return all_triplets
    
    def _generate_cross_chunk_questions_batch(self, chunks: List[ChunkData]) -> List[QATriplet]:
        """Generate cross-chunk questions efficiently."""
        
        cross_triplets = []
        max_cross_questions = min(self.config.max_cross_references, len(chunks) // 3)  # Reduced for efficiency
        
        for _ in range(max_cross_questions):
            # Randomly select two chunks
            chunk1, chunk2 = random.sample(chunks, 2)
            
            # Random difficulty for cross-chunk questions
            difficulty = random.choices(
                [QuestionDifficulty.MEDIUM, QuestionDifficulty.HARD, QuestionDifficulty.VERY_HARD],
                weights=[0.4, 0.4, 0.2]
            )[0]
            
            triplet = self.question_generator.generate_cross_chunk_question(chunk1, chunk2, difficulty)
            
            if triplet:
                cross_triplets.append(triplet)
        
        return cross_triplets
    
    def _generate_chunk_questions(self, chunk: ChunkData) -> List[QATriplet]:
        """Generate questions for a single chunk."""
        
        triplets = []
        questions_to_generate = self.config.questions_per_chunk
        
        for _ in range(questions_to_generate):
            # Select difficulty and type
            difficulty = self._select_weighted_difficulty()
            question_type = self._select_weighted_question_type()
            
            # Generate question
            triplet = self.question_generator.generate_question(chunk, difficulty, question_type)
            
            if triplet:
                triplets.append(triplet)
        
        return triplets
    
    def _generate_cross_chunk_questions(self, chunks: List[ChunkData]) -> List[QATriplet]:
        """Generate cross-chunk questions."""
        
        cross_triplets = []
        max_cross_questions = min(self.config.max_cross_references, len(chunks) // 2)
        
        for _ in range(max_cross_questions):
            # Randomly select two chunks
            chunk1, chunk2 = random.sample(chunks, 2)
            
            # Random difficulty for cross-chunk questions (tend to be harder)
            difficulty = random.choices(
                [QuestionDifficulty.MEDIUM, QuestionDifficulty.HARD, QuestionDifficulty.VERY_HARD],
                weights=[0.3, 0.4, 0.3]
            )[0]
            
            triplet = self.question_generator.generate_cross_chunk_question(chunk1, chunk2, difficulty)
            
            if triplet:
                cross_triplets.append(triplet)
        
        return cross_triplets
    
    def _select_weighted_difficulty(self) -> QuestionDifficulty:
        """Select difficulty based on configured weights."""
        difficulties = list(self.config.difficulty_distribution.keys())
        weights = list(self.config.difficulty_distribution.values())
        
        return random.choices(difficulties, weights=weights)[0]
    
    def _select_weighted_question_type(self) -> QuestionType:
        """Select question type based on configured weights."""
        # Filter out cross-chunk type for single chunk questions
        filtered_distribution = {
            k: v for k, v in self.config.question_type_distribution.items() 
            if k != QuestionType.CROSS_CHUNK
        }
        
        question_types = list(filtered_distribution.keys())
        weights = list(filtered_distribution.values())
        
        return random.choices(question_types, weights=weights)[0]