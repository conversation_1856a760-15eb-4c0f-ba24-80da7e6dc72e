# LM Studio Configuration for QA Generation
LLM_PROVIDER=lm_studio

# LM Studio Settings
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_MODEL=cognitivecomputations/dolphin-mistral-24b-venice-edition

# Batch Processing for 4K Context Limit (optimized for LM Studio)
BATCH_SIZE=3
QUESTIONS_PER_CHUNK=1
ENABLE_CROSS_CHUNK=true
ENABLE_CROSS_SECTION=true
MAX_CROSS_REFERENCES=3

# Question Distribution (optimized for batch processing)
DIFFICULTY_EASY=0.3
DIFFICULTY_MEDIUM=0.4
DIFFICULTY_HARD=0.2
DIFFICULTY_VERY_HARD=0.1

QUESTION_TYPE_FACTUAL=0.3
QUESTION_TYPE_ANALYTICAL=0.2
QUESTION_TYPE_REASONING=0.2
QUESTION_TYPE_CROSS_SECTION=0.15
QUESTION_TYPE_CROSS_CHUNK=0.1
QUESTION_TYPE_COMPARATIVE=0.05

# Output Configuration
OUTPUT_FORMAT=json
VALIDATE_ANSWERS=true
MIN_ANSWER_LENGTH=50
MAX_ANSWER_LENGTH=500