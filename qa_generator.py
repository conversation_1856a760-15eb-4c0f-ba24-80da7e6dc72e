"""
QA Generator for Legal Documents
================================

A comprehensive system for generating question-answer-context triplets from legal document chunks.
Supports multiple LLM providers (Google Gemini, OpenAI, Anthropic) with parametric configuration.

Features:
- Multi-difficulty question generation (Easy, Medium, Hard, Very Hard)
- Cross-chunk and cross-section question generation
- Analytical and reasoning-based questions
- Parametric LLM provider support
- Comprehensive QA triplet validation
- Legal document specialized prompts
"""

from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json
import logging
from datetime import datetime
import os
from abc import ABC, abstractmethod

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QuestionDifficulty(Enum):
    """Question difficulty levels."""
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"
    VERY_HARD = "very_hard"


class QuestionType(Enum):
    """Types of questions to generate."""
    FACTUAL = "factual"
    ANALYTICAL = "analytical"
    REASONING = "reasoning"
    CROSS_SECTION = "cross_section"
    CROSS_CHUNK = "cross_chunk"
    COMPARATIVE = "comparative"
    INFERENTIAL = "inferential"
    APPLICATION = "application"


class LLMProvider(Enum):
    """Supported LLM providers."""
    GOOGLE = "google"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    LM_STUDIO = "lm_studio"
    OPENROUTER = "openrouter"


@dataclass
class QATriplet:
    """Represents a question-answer-context triplet."""
    id: str
    question: str
    answer: str
    context: Union[str, List[Dict[str, Any]]]  # Can be string or list of content objects
    difficulty: QuestionDifficulty
    question_type: QuestionType
    chunk_ids: List[str]
    metadata: Dict[str, Any]
    created_at: str
    context_spans: List[str] = None  # Optional context span references
    evidence: List[str] = None  # List of phrases from context supporting the answer
    
    def __post_init__(self):
        """Initialize context_spans and evidence if not provided."""
        if self.context_spans is None:
            self.context_spans = []
        if self.evidence is None:
            self.evidence = []


@dataclass
class ChunkData:
    """Represents chunk data loaded from JSON."""
    id: str
    content: str
    contextual_summary: str
    sections: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    word_count: int
    character_count: int


@dataclass
class QAGenerationConfig:
    """Configuration for QA generation."""
    llm_provider: LLMProvider = LLMProvider.GOOGLE
    api_key: str = ""
    model_name: str = ""
    base_url: str = "http://localhost:1234/v1"  # For LM Studio
    
    # Question generation parameters
    questions_per_chunk: int = 3
    batch_size: int = 10  # Process chunks in batches to reduce API calls
    difficulty_distribution: Dict[QuestionDifficulty, float] = None
    question_type_distribution: Dict[QuestionType, float] = None
    
    # Cross-reference parameters
    enable_cross_chunk: bool = True
    enable_cross_section: bool = True
    max_cross_references: int = 3
    
    # RefChecker validation parameters
    enable_refchecker: bool = False
    refchecker_model: str = "gpt-4o"  # Model for RefChecker validation
    refchecker_api_key: str = ""  # API key for RefChecker (can be same as main API key)
    factuality_threshold: float = 0.7  # Minimum factuality score to pass
    max_hallucination_rate: float = 0.3  # Maximum allowed hallucination rate
    
    # Output parameters
    output_format: str = "json"
    validate_answers: bool = True
    min_answer_length: int = 50
    max_answer_length: int = 500
    
    def __post_init__(self):
        """Set default distributions if not provided."""
        if self.difficulty_distribution is None:
            self.difficulty_distribution = {
                QuestionDifficulty.EASY: 0.3,
                QuestionDifficulty.MEDIUM: 0.4,
                QuestionDifficulty.HARD: 0.2,
                QuestionDifficulty.VERY_HARD: 0.1
            }
        
        if self.question_type_distribution is None:
            self.question_type_distribution = {
                QuestionType.FACTUAL: 0.3,
                QuestionType.ANALYTICAL: 0.2,
                QuestionType.REASONING: 0.2,
                QuestionType.CROSS_SECTION: 0.1,
                QuestionType.CROSS_CHUNK: 0.1,
                QuestionType.COMPARATIVE: 0.05,
                QuestionType.INFERENTIAL: 0.05
            }


class BaseLLMProvider(ABC):
    """Abstract base class for LLM providers."""
    
    def __init__(self, api_key: str, model_name: str):
        self.api_key = api_key
        self.model_name = model_name
    
    @abstractmethod
    def generate_content(self, prompt: str) -> str:
        """Generate content using the LLM."""
        pass
    
    @abstractmethod
    def validate_configuration(self) -> bool:
        """Validate provider configuration."""
        pass


class GoogleGeminiProvider(BaseLLMProvider):
    """Google Gemini LLM provider."""
    
    def __init__(self, api_key: str, model_name: str = "gemini-2.0-flash-exp"):
        super().__init__(api_key, model_name)
        try:
            import google.generativeai as genai
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel(model_name)
            logger.info(f"Initialized Google Gemini: {model_name}")
        except ImportError:
            raise ImportError("google-generativeai package not installed. Run: pip install google-generativeai")
    
    def generate_content(self, prompt: str) -> str:
        """Generate content using Gemini."""
        try:
            response = self.model.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            logger.error(f"Error generating content with Gemini: {str(e)}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate Gemini configuration."""
        return bool(self.api_key and self.model_name)


class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider."""
    
    def __init__(self, api_key: str, model_name: str = "gpt-4o"):
        super().__init__(api_key, model_name)
        try:
            import openai
            self.client = openai.OpenAI(api_key=api_key)
            logger.info(f"Initialized OpenAI: {model_name}")
        except ImportError:
            raise ImportError("openai package not installed. Run: pip install openai")
    
    def generate_content(self, prompt: str) -> str:
        """Generate content using OpenAI."""
        try:
            # Check if model supports temperature parameter
            api_params = {
                "model": self.model_name,
                "messages": [{"role": "user", "content": prompt}]
            }
            
            # Only add temperature for models that support it
            if not any(model_name in self.model_name.lower() for model_name in ['o1-', 'o3-']):
                api_params["temperature"] = 0.7
            
            response = self.client.chat.completions.create(**api_params)
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error generating content with OpenAI: {str(e)}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate OpenAI configuration."""
        return bool(self.api_key and self.model_name)


class LMStudioProvider(BaseLLMProvider):
    """LM Studio local LLM provider."""
    
    def __init__(self, api_key: str = "", model_name: str = "cognitivecomputations/dolphin-mistral-24b-venice-edition", base_url: str = "http://localhost:1234/v1"):
        super().__init__(api_key or "lm-studio", model_name)
        self.base_url = base_url
        try:
            import openai
            self.client = openai.OpenAI(
                api_key="lm-studio",  # LM Studio doesn't require real API key
                base_url=base_url
            )
            logger.info(f"Initialized LM Studio: {model_name} at {base_url}")
        except ImportError:
            raise ImportError("openai package not installed. Run: pip install openai")
    
    def generate_content(self, prompt: str) -> str:
        """Generate content using LM Studio."""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": prompt}],
                temperature=0.7,
                max_tokens=2048
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error generating content with LM Studio: {str(e)}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate LM Studio configuration."""
        try:
            # Test connection to LM Studio
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return True
        except Exception as e:
            logger.warning(f"LM Studio connection test failed: {str(e)}")
            return False


class AnthropicProvider(BaseLLMProvider):
    """Anthropic Claude LLM provider."""
    
    def __init__(self, api_key: str, model_name: str = "claude-3-5-sonnet-20241022"):
        super().__init__(api_key, model_name)
        try:
            import anthropic
            self.client = anthropic.Anthropic(api_key=api_key)
            logger.info(f"Initialized Anthropic: {model_name}")
        except ImportError:
            raise ImportError("anthropic package not installed. Run: pip install anthropic")
    
    def generate_content(self, prompt: str) -> str:
        """Generate content using Anthropic."""
        try:
            response = self.client.messages.create(
                model=self.model_name,
                max_tokens=2048,
                messages=[{"role": "user", "content": prompt}]
            )
            return response.content[0].text.strip()
        except Exception as e:
            logger.error(f"Error generating content with Anthropic: {str(e)}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate Anthropic configuration."""
        return bool(self.api_key and self.model_name)


class OpenRouterProvider(BaseLLMProvider):
    """OpenRouter LLM provider for DeepSeek and other models."""
    
    def __init__(self, api_key: str, model_name: str = "deepseek/deepseek-chat-v3.1:free"):
        super().__init__(api_key, model_name)
        try:
            import openai
            self.client = openai.OpenAI(
                api_key=api_key,
                base_url="https://openrouter.ai/api/v1"
            )
            logger.info(f"Initialized OpenRouter: {model_name}")
        except ImportError:
            raise ImportError("openai package not installed. Run: pip install openai")
    
    def generate_content(self, prompt: str) -> str:
        """Generate content using OpenRouter."""
        try:
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                temperature=0.7,
                max_tokens=2048
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            logger.error(f"Error generating content with OpenRouter: {str(e)}")
            raise
    
    def validate_configuration(self) -> bool:
        """Validate OpenRouter configuration."""
        if not self.api_key or not self.model_name:
            return False
        
        try:
            # Test connection to OpenRouter
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[{"role": "user", "content": "Hello"}],
                max_tokens=10
            )
            return True
        except Exception as e:
            logger.warning(f"OpenRouter connection test failed: {str(e)}")
            return False


class LLMProviderFactory:
    """Factory for creating LLM providers."""
    
    @staticmethod
    def create_provider(provider_type: LLMProvider, api_key: str, model_name: str = "") -> BaseLLMProvider:
        """Create an LLM provider instance."""
        
        if provider_type == LLMProvider.GOOGLE:
            model_name = model_name or "gemini-2.0-flash-exp"
            return GoogleGeminiProvider(api_key, model_name)
        
        elif provider_type == LLMProvider.OPENAI:
            model_name = model_name or "gpt-4o"
            return OpenAIProvider(api_key, model_name)
        
        elif provider_type == LLMProvider.ANTHROPIC:
            model_name = model_name or "claude-3-5-sonnet-20241022"
            return AnthropicProvider(api_key, model_name)
        
        elif provider_type == LLMProvider.LM_STUDIO:
            model_name = model_name or "cognitivecomputations/dolphin-mistral-24b-venice-edition"
            return LMStudioProvider(api_key, model_name)
        
        elif provider_type == LLMProvider.OPENROUTER:
            model_name = model_name or "deepseek/deepseek-chat-v3.1:free"
            return OpenRouterProvider(api_key, model_name)
        
        else:
            raise ValueError(f"Unsupported LLM provider: {provider_type}")


class QAGenerator:
    """Main QA generation class."""
    
    def __init__(self, config: QAGenerationConfig):
        self.config = config
        self.chunks: List[ChunkData] = []
        self.qa_triplets: List[QATriplet] = []
        
        # Initialize LLM provider
        if config.llm_provider == LLMProvider.LM_STUDIO:
            self.llm_provider = LMStudioProvider(
                config.api_key,
                config.model_name,
                config.base_url
            )
        elif config.llm_provider == LLMProvider.OPENROUTER:
            self.llm_provider = OpenRouterProvider(
                config.api_key,
                config.model_name
            )
        else:
            self.llm_provider = LLMProviderFactory.create_provider(
                config.llm_provider,
                config.api_key,
                config.model_name
            )
        
        if not self.llm_provider.validate_configuration():
            raise ValueError("Invalid LLM provider configuration")
        
        logger.info(f"QA Generator initialized with {config.llm_provider.value} provider")
    
    def load_chunks(self, chunks_file: str) -> List[ChunkData]:
        """Load chunks from JSON file."""
        logger.info(f"Loading chunks from {chunks_file}")
        
        with open(chunks_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        chunks_data = data.get('chunks', [])
        chunks = []
        
        for chunk_data in chunks_data:
            chunk = ChunkData(
                id=chunk_data['id'],
                content=chunk_data['content'],
                contextual_summary=chunk_data.get('contextual_summary', ''),
                sections=chunk_data.get('sections', []),
                metadata=chunk_data.get('metadata', {}),
                word_count=chunk_data.get('word_count', 0),
                character_count=chunk_data.get('character_count', 0)
            )
            chunks.append(chunk)
        
        self.chunks = chunks
        logger.info(f"Loaded {len(chunks)} chunks")
        return chunks
    
    def generate_qa_triplets(self) -> List[QATriplet]:
        """Generate QA triplets from loaded chunks."""
        logger.info("Starting QA triplet generation...")
        
        if not self.chunks:
            raise ValueError("No chunks loaded. Call load_chunks() first.")
        
        # Import here to avoid circular imports
        from question_engine import QuestionGenerator, QABatchGenerator
        
        # Initialize question generator
        question_generator = QuestionGenerator(self.llm_provider, self.config)
        batch_generator = QABatchGenerator(question_generator, self.config)
        
        # Generate QA triplets
        qa_triplets = batch_generator.generate_batch_for_chunks(self.chunks)
        
        # Apply RefChecker validation if enabled
        if self.config.enable_refchecker:
            logger.info("Applying RefChecker validation to QA triplets...")
            qa_triplets = self._apply_refchecker_validation(qa_triplets)
        
        self.qa_triplets = qa_triplets
        logger.info(f"Generated {len(qa_triplets)} QA triplets total")
        return qa_triplets
    
    def _generate_questions_for_chunk(self, chunk: ChunkData) -> List[QATriplet]:
        """Generate questions for a single chunk."""
        # This method is now handled by QABatchGenerator
        pass
    
    def _generate_single_question(self, chunk: ChunkData, difficulty: QuestionDifficulty, 
                                question_type: QuestionType) -> Optional[QATriplet]:
        """Generate a single QA triplet."""
        # This method is now handled by QuestionGenerator
        pass
    
    def _generate_cross_chunk_questions(self) -> List[QATriplet]:
        """Generate questions that span multiple chunks."""
        # This method is now handled by QABatchGenerator
        pass
    
    def _select_difficulty(self) -> QuestionDifficulty:
        """Select difficulty based on configured distribution."""
        import random
        
        rand_val = random.random()
        cumulative = 0
        
        for difficulty, probability in self.config.difficulty_distribution.items():
            cumulative += probability
            if rand_val <= cumulative:
                return difficulty
        
        return QuestionDifficulty.MEDIUM  # fallback
    
    def _select_question_type(self) -> QuestionType:
        """Select question type based on configured distribution."""
        import random
        
        rand_val = random.random()
        cumulative = 0
        
        for q_type, probability in self.config.question_type_distribution.items():
            cumulative += probability
            if rand_val <= cumulative:
                return q_type
        
        return QuestionType.FACTUAL  # fallback
    
    def _apply_refchecker_validation(self, qa_triplets: List[QATriplet]) -> List[QATriplet]:
        """Apply RefChecker validation to filter out hallucinated QA triplets."""
        try:
            from refchecker_validator import RefCheckerValidator, RefCheckerConfig, CheckerType
            
            # Configure RefChecker
            refchecker_config = RefCheckerConfig(
                checker_type=CheckerType.LLM_CHECKER,
                model_name=self.config.refchecker_model,
                api_key=self.config.refchecker_api_key or self.config.api_key,
                factuality_threshold=self.config.factuality_threshold,
                max_hallucination_rate=self.config.max_hallucination_rate,
                batch_size=self.config.batch_size
            )
            
            # Initialize validator
            validator = RefCheckerValidator(refchecker_config)
            
            # Convert QATriplets to dictionaries for validation
            qa_dicts = []
            for triplet in qa_triplets:
                qa_dict = {
                    'id': triplet.id,
                    'question': triplet.question,
                    'answer': triplet.answer,
                    'context': triplet.context
                }
                qa_dicts.append(qa_dict)
            
            # Validate and filter triplets
            validated_dicts, validation_reports = validator.filter_validated_triplets(qa_dicts)
            
            # Convert back to QATriplet objects, keeping only validated ones
            validated_triplets = []
            validated_ids = {qa_dict['id'] for qa_dict in validated_dicts}
            
            for triplet in qa_triplets:
                if triplet.id in validated_ids:
                    # Add validation metadata to the triplet
                    matching_report = next(
                        (r for r in validation_reports if r.qa_triplet_id == triplet.id),
                        None
                    )
                    if matching_report:
                        triplet.metadata.update({
                            'refchecker_validation': {
                                'passed': True,
                                'factuality_score': matching_report.overall_factuality_score,
                                'hallucination_rate': matching_report.hallucination_rate,
                                'total_claims': matching_report.total_claims,
                                'factual_claims': matching_report.factual_claims,
                                'validated_at': matching_report.validation_timestamp
                            }
                        })
                    validated_triplets.append(triplet)
            
            # Log validation results
            original_count = len(qa_triplets)
            validated_count = len(validated_triplets)
            filtered_count = original_count - validated_count
            
            logger.info(
                f"RefChecker validation complete: {validated_count}/{original_count} triplets passed "
                f"({validated_count/original_count*100:.1f}%), {filtered_count} filtered out"
            )
            
            # Save validation report if needed
            if validation_reports:
                self._save_validation_report(validation_reports)
            
            return validated_triplets
            
        except ImportError:
            logger.warning(
                "RefChecker not available. Install with: pip install refchecker && python -m spacy download en_core_web_sm"
            )
            return qa_triplets
        except Exception as e:
            logger.error(f"RefChecker validation failed: {e}")
            logger.warning("Continuing without RefChecker validation")
            return qa_triplets
    
    def _save_validation_report(self, validation_reports) -> None:
        """Save RefChecker validation report to file."""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = f"refchecker_validation_report_{timestamp}.json"
            
            # Convert reports to dictionaries
            report_data = {
                'validation_timestamp': datetime.now().isoformat(),
                'total_triplets_validated': len(validation_reports),
                'passed_validation': sum(1 for r in validation_reports if r.passed_validation),
                'failed_validation': sum(1 for r in validation_reports if not r.passed_validation),
                'average_factuality_score': sum(r.overall_factuality_score for r in validation_reports) / len(validation_reports) if validation_reports else 0,
                'config': {
                    'factuality_threshold': self.config.factuality_threshold,
                    'max_hallucination_rate': self.config.max_hallucination_rate,
                    'refchecker_model': self.config.refchecker_model
                },
                'detailed_reports': []
            }
            
            for report in validation_reports:
                report_dict = {
                    'qa_triplet_id': report.qa_triplet_id,
                    'passed_validation': report.passed_validation,
                    'total_claims': report.total_claims,
                    'factual_claims': report.factual_claims,
                    'hallucinated_claims': report.hallucinated_claims,
                    'neutral_claims': report.neutral_claims,
                    'overall_factuality_score': report.overall_factuality_score,
                    'hallucination_rate': report.hallucination_rate,
                    'factuality_rate': report.factuality_rate,
                    'validation_timestamp': report.validation_timestamp,
                    'checker_type': report.checker_type.value,
                    'model_used': report.model_used,
                    'claims': [
                        {
                            'content': claim.content,
                            'validation_result': claim.validation_result.value,
                            'confidence_score': claim.confidence_score,
                            'supporting_evidence': claim.supporting_evidence
                        }
                        for claim in report.claims
                    ]
                }
                report_data['detailed_reports'].append(report_dict)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"RefChecker validation report saved to {report_file}")
            
        except Exception as e:
            logger.error(f"Failed to save validation report: {e}")
    
    def save_qa_triplets(self, output_file: str) -> None:
        """Save QA triplets to file."""
        logger.info(f"Saving {len(self.qa_triplets)} QA triplets to {output_file}")
        
        # Create a serializable config dict
        config_dict = {
            'llm_provider': self.config.llm_provider.value,
            'api_key': '***hidden***',  # Don't save API key
            'model_name': self.config.model_name,
            'base_url': self.config.base_url,
            'questions_per_chunk': self.config.questions_per_chunk,
            'batch_size': self.config.batch_size,
            'enable_cross_chunk': self.config.enable_cross_chunk,
            'enable_cross_section': self.config.enable_cross_section,
            'max_cross_references': self.config.max_cross_references,
            'enable_refchecker': self.config.enable_refchecker,
            'refchecker_model': self.config.refchecker_model,
            'refchecker_api_key': '***hidden***' if self.config.refchecker_api_key else '',
            'factuality_threshold': self.config.factuality_threshold,
            'max_hallucination_rate': self.config.max_hallucination_rate,
            'output_format': self.config.output_format,
            'validate_answers': self.config.validate_answers,
            'min_answer_length': self.config.min_answer_length,
            'max_answer_length': self.config.max_answer_length,
            'difficulty_distribution': {k.value: v for k, v in self.config.difficulty_distribution.items()},
            'question_type_distribution': {k.value: v for k, v in self.config.question_type_distribution.items()}
        }
        
        # Create serializable triplet dicts
        triplet_dicts = []
        for triplet in self.qa_triplets:
            # Make metadata serializable by converting any enum values to strings
            serializable_metadata = {}
            for key, value in triplet.metadata.items():
                if hasattr(value, 'value'):  # Check if it's an enum
                    serializable_metadata[key] = value.value
                elif isinstance(value, list):
                    # Handle lists that might contain enums
                    serializable_metadata[key] = [v.value if hasattr(v, 'value') else v for v in value]
                else:
                    serializable_metadata[key] = value
            
            triplet_dict = {
                'id': triplet.id,
                'question': triplet.question,
                'answer': triplet.answer,
                'context': triplet.context,
                'difficulty': triplet.difficulty.value,
                'question_type': triplet.question_type.value,
                'chunk_ids': triplet.chunk_ids,
                'context_spans': triplet.context_spans if hasattr(triplet, 'context_spans') else [],
                'evidence': triplet.evidence if hasattr(triplet, 'evidence') else [],  # Include evidence field
                'metadata': serializable_metadata,
                'created_at': triplet.created_at
            }
            triplet_dicts.append(triplet_dict)
        
        output_data = {
            'config': config_dict,
            'generation_info': {
                'total_triplets': len(self.qa_triplets),
                'chunks_processed': len(self.chunks),
                'created_at': datetime.now().isoformat(),
                'llm_provider': self.config.llm_provider.value,
                'model_name': self.config.model_name
            },
            'qa_triplets': triplet_dicts
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"QA triplets saved to {output_file}")


def main():
    """Main function for testing."""
    import argparse
    
    parser = argparse.ArgumentParser(description='QA Generator for Legal Documents')
    parser.add_argument('--chunks-file', required=True, help='Input chunks JSON file')
    parser.add_argument('--output', required=True, help='Output QA triplets file')
    parser.add_argument('--provider', choices=['google', 'openai', 'anthropic'], 
                       default='google', help='LLM provider')
    parser.add_argument('--api-key', required=True, help='API key for LLM provider')
    parser.add_argument('--model', help='Model name (optional)')
    parser.add_argument('--questions-per-chunk', type=int, default=3, 
                       help='Questions per chunk')
    
    args = parser.parse_args()
    
    # Create configuration
    config = QAGenerationConfig(
        llm_provider=LLMProvider(args.provider),
        api_key=args.api_key,
        model_name=args.model or "",
        questions_per_chunk=args.questions_per_chunk
    )
    
    # Initialize generator
    generator = QAGenerator(config)
    
    # Load chunks and generate QA triplets
    generator.load_chunks(args.chunks_file)
    generator.generate_qa_triplets()
    
    # Save results
    generator.save_qa_triplets(args.output)
    
    print(f"✅ Generated {len(generator.qa_triplets)} QA triplets")
    print(f"📁 Output saved to: {args.output}")


if __name__ == "__main__":
    main()