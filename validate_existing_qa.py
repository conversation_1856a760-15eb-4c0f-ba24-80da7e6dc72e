#!/usr/bin/env python3
"""
Validate Existing QA Triplets with RefChecker
============================================

This script takes existing QA triplets and runs Ref<PERSON><PERSON><PERSON> validation
to generate a validation report showing which triplets have hallucinated content.
"""

import json
import os
import argparse
from datetime import datetime
from refchecker_validator import RefCheckerValidator, RefCheckerConfig, CheckerType

def load_qa_triplets(file_path):
    """Load QA triplets from JSON file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data.get('qa_triplets', [])

def convert_qa_triplet_format(qa_triplet):
    """Convert QA triplet to format expected by <PERSON>f<PERSON><PERSON><PERSON>."""
    return {
        "id": qa_triplet.get("id", "unknown"),
        "question": qa_triplet.get("question", ""),
        "answer": qa_triplet.get("answer", ""),
        "context": qa_triplet.get("context", [])
    }

def save_validation_report(reports, config, output_file):
    """Save detailed validation report."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"refchecker_validation_report_{timestamp}.json"
    
    # Calculate summary statistics
    total_triplets = len(reports)
    passed = sum(1 for r in reports if r.passed_validation)
    failed = total_triplets - passed
    
    if total_triplets > 0:
        avg_factuality = sum(r.overall_factuality_score for r in reports) / total_triplets
    else:
        avg_factuality = 0.0
    
    # Create comprehensive report
    report_data = {
        "validation_timestamp": datetime.now().isoformat(),
        "total_triplets_validated": total_triplets,
        "passed_validation": passed,
        "failed_validation": failed,
        "success_rate": passed / total_triplets if total_triplets > 0 else 0,
        "average_factuality_score": avg_factuality,
        "config": {
            "checker_type": config.checker_type.value,
            "model_name": config.model_name,
            "factuality_threshold": config.factuality_threshold,
            "max_hallucination_rate": config.max_hallucination_rate,
            "refchecker_model": config.model_name
        },
        "detailed_reports": [
            {
                "qa_triplet_id": r.qa_triplet_id,
                "passed_validation": r.passed_validation,
                "total_claims": r.total_claims,
                "factual_claims": r.factual_claims,
                "hallucinated_claims": r.hallucinated_claims,
                "neutral_claims": r.neutral_claims,
                "overall_factuality_score": r.overall_factuality_score,
                "hallucination_rate": r.hallucination_rate,
                "validation_timestamp": r.validation_timestamp,
                "claims": [
                    {
                        "content": claim.content,
                        "validation_result": claim.validation_result.value,
                        "confidence_score": claim.confidence_score,
                        "supporting_evidence": claim.supporting_evidence
                    }
                    for claim in r.claims
                ]
            }
            for r in reports
        ]
    }
    
    # Save report
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"📊 Validation report saved to: {report_file}")
    return report_file

def main():
    parser = argparse.ArgumentParser(description='Validate existing QA triplets with RefChecker')
    parser.add_argument('--qa-file', required=True, help='QA triplets JSON file to validate')
    parser.add_argument('--api-key', help='API key for RefChecker (OpenAI)')
    parser.add_argument('--model', default='gpt-4o', help='Model to use for RefChecker')
    parser.add_argument('--factuality-threshold', type=float, default=0.7, 
                       help='Minimum factuality score to pass validation')
    parser.add_argument('--max-hallucination-rate', type=float, default=0.3,
                       help='Maximum allowed hallucination rate')
    
    args = parser.parse_args()
    
    # Get API key from environment if not provided
    if not args.api_key:
        args.api_key = os.getenv('OPENAI_API_KEY')
        if not args.api_key:
            print("❌ Error: API key required. Provide --api-key or set OPENAI_API_KEY environment variable")
            return 1
    
    print("🔍 RefChecker Validation of Existing QA Triplets")
    print("=" * 60)
    
    try:
        # Load QA triplets
        print(f"📂 Loading QA triplets from: {args.qa_file}")
        qa_triplets = load_qa_triplets(args.qa_file)
        print(f"✅ Loaded {len(qa_triplets)} QA triplets")
        
        # Configure RefChecker
        config = RefCheckerConfig(
            checker_type=CheckerType.LLM_CHECKER,
            model_name=args.model,
            api_key=args.api_key,
            factuality_threshold=args.factuality_threshold,
            max_hallucination_rate=args.max_hallucination_rate,
            batch_size=2  # Small batch for stability
        )
        
        print(f"⚙️ RefChecker Configuration:")
        print(f"  Model: {config.model_name}")
        print(f"  Factuality threshold: {config.factuality_threshold}")
        print(f"  Max hallucination rate: {config.max_hallucination_rate}")
        
        # Initialize validator
        print(f"\n🤖 Initializing RefChecker validator...")
        validator = RefCheckerValidator(config)
        
        # Convert QA triplets to RefChecker format
        converted_triplets = [convert_qa_triplet_format(qa) for qa in qa_triplets]
        
        # Validate triplets
        print(f"\n🔍 Validating {len(converted_triplets)} QA triplets...")
        validation_reports = validator.validate_qa_batch(converted_triplets)
        
        # Save validation report
        report_file = save_validation_report(validation_reports, config, args.qa_file)
        
        # Print summary
        total = len(validation_reports)
        passed = sum(1 for r in validation_reports if r.passed_validation)
        failed = total - passed
        
        print(f"\n📈 Validation Summary:")
        print(f"  Total triplets: {total}")
        print(f"  ✅ Passed: {passed} ({passed/total*100:.1f}%)")
        print(f"  ❌ Failed: {failed} ({failed/total*100:.1f}%)")
        
        if failed > 0:
            print(f"\n❌ Failed Triplets (with contradictory claims):")
            failed_reports = [r for r in validation_reports if not r.passed_validation]
            for report in failed_reports[:5]:  # Show first 5
                print(f"  - {report.qa_triplet_id}: Factuality {report.overall_factuality_score:.2f}, "
                      f"Hallucination Rate {report.hallucination_rate:.2%}")
        
        print(f"\n🔍 To analyze the report:")
        print(f"  python demo_refchecker.py --analyze-report {report_file}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit(main())