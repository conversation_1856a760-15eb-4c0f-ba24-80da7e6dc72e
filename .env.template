# QA Generator Configuration Template
# ==================================
# Copy this file to .env and configure your settings

# LLM Provider Selection (parameterized)
# Supported providers: google, openai, anthropic, lm_studio
LLM_PROVIDER=lm_studio

# API Keys (required for cloud providers)
GOOGLE_API_KEY=your_google_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
# LM Studio doesn't require an API key (local inference)

# Model Configuration (parameterized by provider)
# Google Models
GOOGLE_MODEL=gemini-2.0-flash-exp
# Alternatives: gemini-1.5-pro, gemini-1.5-flash

# OpenAI Models  
OPENAI_MODEL=gpt-4o
# Alternatives: gpt-4, gpt-4-turbo, gpt-3.5-turbo

# Anthropic Models
ANTHROPIC_MODEL=claude-3-5-sonnet-20241022
# Alternatives: claude-3-opus-20240229, claude-3-haiku-20240307

# LM Studio Configuration (local inference)
LM_STUDIO_BASE_URL=http://localhost:1234/v1
LM_STUDIO_MODEL=cognitivecomputations/dolphin-mistral-24b-venice-edition
# Alternative models: any model loaded in LM Studio

# QA Generation Parameters
QUESTIONS_PER_CHUNK=2
BATCH_SIZE=3
ENABLE_CROSS_CHUNK=true
ENABLE_CROSS_SECTION=true
MAX_CROSS_REFERENCES=3

# Quality Control
VALIDATE_ANSWERS=true
MIN_ANSWER_LENGTH=50
MAX_ANSWER_LENGTH=500

# Difficulty Distribution (should sum to 1.0)
DIFFICULTY_EASY=0.3
DIFFICULTY_MEDIUM=0.4
DIFFICULTY_HARD=0.2
DIFFICULTY_VERY_HARD=0.1

# Question Type Distribution (should sum to 1.0)
QUESTION_TYPE_FACTUAL=0.3
QUESTION_TYPE_ANALYTICAL=0.2
QUESTION_TYPE_REASONING=0.2
QUESTION_TYPE_CROSS_SECTION=0.15
QUESTION_TYPE_CROSS_CHUNK=0.1
QUESTION_TYPE_COMPARATIVE=0.05
QUESTION_TYPE_INFERENTIAL=0.0
QUESTION_TYPE_APPLICATION=0.0

# Output Configuration
OUTPUT_FORMAT=json

# Command Line Usage Examples:
# ============================
# 
# Use environment configuration:
#   python run_qa_generator.py
#
# Override provider:
#   python run_qa_generator.py --provider google --api-key YOUR_KEY
#
# Test with subset:
#   python test_qa_with_subset.py --provider lm_studio
#
# Custom model:
#   python run_qa_generator.py --provider openai --model gpt-4 --api-key YOUR_KEY
#
# Specific input file:
#   python run_qa_generator.py --input test_chunks_5.json --provider lm_studio