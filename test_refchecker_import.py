#!/usr/bin/env python3
"""
Test RefChecker Import
=====================

Simple script to test if RefChecker can be imported successfully.
"""

def test_refchecker_import():
    """Test RefChecker import."""
    try:
        print("🔍 Testing RefChecker import...")
        
        # Test basic imports
        from refchecker import LLMExtractor, LLMChecker
        print("✅ RefChecker core components imported successfully")
        
        # Test additional imports
        try:
            from refchecker import AlignScoreChecker, NLIChecker
            print("✅ RefChecker additional components imported successfully")
        except ImportError as e:
            print(f"⚠️  Some RefChecker components unavailable: {e}")
        
        # Test our validator import
        try:
            from refchecker_validator import RefCheckerValidator, RefCheckerConfig
            print("✅ Our RefChecker validator imported successfully")
        except ImportError as e:
            print(f"❌ Our RefChecker validator import failed: {e}")
            return False
        
        print("\n🎉 <PERSON>fChecker is ready to use!")
        return True
        
    except ImportError as e:
        print(f"❌ RefChecker import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = test_refchecker_import()
    exit(0 if success else 1)