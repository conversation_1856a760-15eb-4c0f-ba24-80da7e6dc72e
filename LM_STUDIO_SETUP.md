# LM Studio Setup Guide

This guide explains how to set up LM Studio with the Dolphin Mistral 24B Venice Edition model for local QA generation without API rate limits.

## Prerequisites

- **LM Studio** installed on your system
- At least **16GB RAM** (24GB+ recommended for 24B model)
- **NVIDIA GPU with 12GB+ VRAM** (optional but recommended for faster inference)

## Step 1: Install LM Studio

1. Download LM Studio from: https://lmstudio.ai/
2. Install and launch the application

## Step 2: Download the Dolphin Mistral 24B Venice Edition Model

1. Open LM Studio
2. Go to the **Models** tab
3. Search for: `cognitivecomputations/dolphin-mistral-24b-venice-edition`
4. Select an appropriate quantization (GGUF format):
   - **Q4_K_M** - Good balance of quality and performance (recommended)
   - **Q5_K_M** - Higher quality, more VRAM required
   - **Q8_0** - Highest quality, requires significant VRAM
5. Click **Download**

## Step 3: Start the Local Server

1. Go to the **Local Server** tab in LM Studio
2. Select your downloaded Dolphin Mistral model
3. Configure settings:
   - **Context Length**: 4096 or higher
   - **GPU Layers**: Adjust based on your GPU VRAM
   - **Temperature**: 0.7 (good for QA generation)
4. Click **Start Server**
5. Note the server URL (usually `http://localhost:1234`)

## Step 4: Configure QA Generator

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file:
   ```env
   # Set LM Studio as provider
   LLM_PROVIDER=lm_studio
   
   # LM Studio Configuration
   LM_STUDIO_BASE_URL=http://localhost:1234/v1
   LM_STUDIO_MODEL=cognitivecomputations/dolphin-mistral-24b-venice-edition
   
   # Batch Processing for Efficiency
   BATCH_SIZE=10
   QUESTIONS_PER_CHUNK=2
   ```

## Step 5: Test the Setup

Run a quick test:

```bash
python test_qa_generator.py
```

If successful, you should see LM Studio initialization messages.

## Step 6: Generate QA Triplets

Run the full QA generation:

```bash
python run_qa_generator.py
```

## Benefits of LM Studio Setup

✅ **No API Rate Limits** - Generate unlimited questions locally  
✅ **No API Costs** - Free after initial setup  
✅ **Privacy** - All processing stays on your machine  
✅ **Batch Processing** - Process 10 chunks at once for efficiency  
✅ **Consistent Performance** - No network dependencies  

## Optimizations for Large Documents

### Batch Processing Benefits
- **10 chunks per API call** instead of 1
- **Reduced prompt overhead** and context switching
- **Better coherence** across related sections
- **Faster overall processing** time

### Memory Management
- LM Studio automatically manages model memory
- Adjust GPU layers based on available VRAM
- Use CPU fallback for layers that don't fit in GPU

### Performance Tips

1. **Model Selection**:
   - Q4_K_M: Fast, good quality (recommended)
   - Q5_K_M: Better quality, slower
   - Q8_0: Best quality, slowest

2. **Context Length**:
   - Use 4096+ for batch processing
   - Higher values allow more chunks per batch

3. **Temperature Settings**:
   - 0.7: Good balance for legal QA
   - 0.5: More conservative/consistent
   - 0.9: More creative/diverse

## Troubleshooting

### Common Issues

**Model won't load:**
- Check available RAM/VRAM
- Try a smaller quantization (Q4_K_S instead of Q4_K_M)
- Reduce GPU layers

**Server connection error:**
- Ensure LM Studio server is running
- Check the correct port (default: 1234)
- Verify firewall settings

**Slow generation:**
- Enable GPU acceleration
- Use appropriate quantization for your hardware
- Reduce batch size if memory is limited

### Performance Monitoring

Monitor in LM Studio:
- **Tokens/second**: Generation speed
- **GPU utilization**: Hardware usage
- **Memory usage**: RAM/VRAM consumption

## Expected Performance

With the optimized batch processing:

- **Luxembourg Civil Code** (209 chunks)
- **Batch size**: 10 chunks
- **Processing time**: ~21 API calls instead of 600+
- **Total time**: 15-30 minutes (vs hours with individual calls)
- **Questions generated**: 400+ high-quality QA triplets

This setup provides an efficient, cost-effective solution for large-scale legal document QA generation!