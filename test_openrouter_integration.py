#!/usr/bin/env python3
"""
Test script for OpenRouter integration with DeepSeek Chat v3.1:free model
"""

import os
import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

def test_openrouter_qa_generation():
    """Test OpenRouter provider with DeepSeek model."""
    
    print("🧪 Testing OpenRouter Integration with DeepSeek")
    print("=" * 60)
    
    # Check if OpenRouter API key is available
    api_key = os.getenv('OPENROUTER_API_KEY')
    
    if not api_key:
        print("❌ OPENROUTER_API_KEY not found in environment variables")
        print()
        print("💡 To test OpenRouter integration:")
        print("1. Get your API key from https://openrouter.ai/")
        print("2. Set environment variable: OPENROUTER_API_KEY=your_key_here")
        print("3. Or add it to your .env file")
        print()
        print("🆓 Benefits of DeepSeek Chat v3.1:free:")
        print("   • Free model (no cost)")
        print("   • Good performance for QA generation")
        print("   • High rate limits")
        print("   • Easy integration")
        return False
    
    print(f"✅ OpenRouter API key found: {api_key[:8]}...")
    print()
    
    try:
        from qa_generator import QAGenerationConfig, QAGenerator, LLMProvider
        
        # Test configuration
        config = QAGenerationConfig(
            llm_provider=LLMProvider.OPENROUTER,
            api_key=api_key,
            model_name="deepseek/deepseek-chat-v3.1:free",
            questions_per_chunk=2,
            batch_size=5
        )
        
        print("🔧 Testing OpenRouter provider configuration...")
        generator = QAGenerator(config)
        print("✅ OpenRouter provider initialized successfully")
        
        # Check if test chunks file exists
        test_files = [
            'test_chunks_5.json',
            '../chunks_output.json',
            '../chunks_with_summaries.json'
        ]
        
        input_file = None
        for file in test_files:
            if os.path.exists(file):
                input_file = file
                break
        
        if not input_file:
            print("⚠️  No test chunks file found")
            print("   To run full test, first generate chunks:")
            print("   cd .. && python run_chunker.py")
            return True
        
        print(f"📖 Loading chunks from {input_file}...")
        chunks = generator.load_chunks(input_file)
        print(f"   Loaded {len(chunks)} chunks")
        
        # Test with a small subset for quick validation
        generator.chunks = chunks[:2]  # Test with first 2 chunks only
        
        print("🧠 Testing QA generation with OpenRouter/DeepSeek...")
        triplets = generator.generate_qa_triplets()
        
        output_file = 'test_openrouter_qa_output.json'
        generator.save_qa_triplets(output_file)
        
        print(f"✅ Success! Generated {len(triplets)} QA triplets")
        print(f"📁 Output saved to: {output_file}")
        
        # Show sample output
        if triplets:
            print("\\n📋 Sample QA Triplet:")
            sample = triplets[0]
            print(f"   Question: {sample.question[:100]}...")
            print(f"   Difficulty: {sample.difficulty.value}")
            print(f"   Type: {sample.question_type.value}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ Error testing OpenRouter: {str(e)}")
        return False

def main():
    """Main test function."""
    print("🚀 OpenRouter + DeepSeek Integration Test")
    print()
    
    success = test_openrouter_qa_generation()
    
    if success:
        print("\\n🎯 OpenRouter Integration Ready!")
        print()
        print("💡 Usage Examples:")
        print("   # Basic usage with OpenRouter")
        print("   python run_qa_generator.py --provider openrouter --api-key YOUR_KEY")
        print()
        print("   # With custom model")
        print("   python run_qa_generator.py --provider openrouter --api-key YOUR_KEY --model deepseek/deepseek-r1-distill-llama-70b")
        print()
        print("   # Environment variable approach")
        print("   export OPENROUTER_API_KEY=your_key")
        print("   python run_qa_generator.py --provider openrouter")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())