#!/usr/bin/env python3
"""
Test script to verify the new context structure with list of content objects
"""

import sys
import os
import json
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider

def test_context_structure():
    """Test that context is now a list of content objects with line spans."""
    
    print("🧪 Testing New Context Structure")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if test chunks file exists
    test_chunks_file = "test_chunks_5.json"
    if not Path(test_chunks_file).exists():
        print(f"❌ Test chunks file {test_chunks_file} not found!")
        return 1
    
    # Create configuration for LM Studio
    config = QAGenerationConfig(
        llm_provider=LLMProvider.LM_STUDIO,
        api_key='',  # Not needed for LM Studio
        model_name='cognitivecomputations/dolphin-mistral-24b-venice-edition',
        base_url='http://localhost:1234/v1',
        questions_per_chunk=1,  # 1 question per chunk for testing
        batch_size=1,  # Process 1 chunk at a time for testing
        enable_cross_chunk=False,  # Disable cross-chunk for testing
        min_answer_length=30,
        max_answer_length=300
    )
    
    print(f"📋 Configuration:")
    print(f"   Provider: {config.llm_provider.value}")
    print(f"   Model: {config.model_name}")
    print(f"   Base URL: {config.base_url}")
    
    try:
        # Initialize generator
        print(f"\n🚀 Initializing QA Generator...")
        generator = QAGenerator(config)
        
        # Load test chunks (just first one for quick test)
        print(f"📖 Loading test chunks from {test_chunks_file}...")
        all_chunks = generator.load_chunks(test_chunks_file)
        chunk = all_chunks[0]  # Use just first chunk for testing
        print(f"   Using chunk: {chunk.id}")
        
        # Show original sections structure
        print(f"\n📊 Original chunk sections:")
        for i, section in enumerate(chunk.sections):
            print(f"   {i+1}. {section.get('level', 'unknown')} {section.get('number', 'N/A')}")
            print(f"      Content: {section.get('content', '')[:80]}...")
            print(f"      Line span: {section.get('line_start', 0)}-{section.get('line_end', 0)}")
        
        # Generate QA triplets
        print(f"\n🧠 Generating QA triplets with new context structure...")
        triplets = generator.generate_qa_triplets()
        
        if triplets:
            print(f"\n✅ Generated {len(triplets)} QA triplets!")
            
            # Examine the context structure
            sample = triplets[0]
            print(f"\n📝 Sample QA triplet with new context structure:")
            print(f"   Question: {sample.question}")
            print(f"   Answer: {sample.answer[:100]}...")
            print(f"   Context type: {type(sample.context)}")
            
            if isinstance(sample.context, list):
                print(f"   ✅ Context is a list with {len(sample.context)} items:")
                for i, content_obj in enumerate(sample.context):
                    if isinstance(content_obj, dict):
                        print(f"      {i+1}. Content: {content_obj.get('content', '')[:60]}...")
                        print(f"         Line span: {content_obj.get('line_start', 0)}-{content_obj.get('line_end', 0)}")
                    else:
                        print(f"      {i+1}. Unexpected type: {type(content_obj)}")
            else:
                print(f"   ❌ Context is not a list: {type(sample.context)}")
                print(f"       Content preview: {str(sample.context)[:100]}...")
            
            print(f"\n   Context spans: {sample.context_spans}")
            
            # Save results for inspection
            output_file = "test_qa_output_new_context.json"
            generator.save_qa_triplets(output_file)
            print(f"\n💾 Results saved to: {output_file}")
            
            # Verify JSON structure
            with open(output_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            if saved_data.get('qa_triplets'):
                first_triplet = saved_data['qa_triplets'][0]
                context_data = first_triplet.get('context')
                
                print(f"\n🔍 Verification of saved JSON:")
                print(f"   Context type in JSON: {type(context_data)}")
                if isinstance(context_data, list) and context_data:
                    print(f"   ✅ Context saved as list with {len(context_data)} items")
                    print(f"   Sample context item keys: {list(context_data[0].keys()) if context_data[0] else 'empty'}")
                else:
                    print(f"   ❌ Context not saved as expected list structure")
            
        else:
            print(f"\n❌ No triplets generated!")
            return 1
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    print("⚠️  Note: This test requires LM Studio to be running on localhost:1234")
    print("   If LM Studio is not available, this will demonstrate the context structure only.")
    print("   The key change is that context is now a list of content objects with line spans.")
    print()
    
    sys.exit(test_context_structure())