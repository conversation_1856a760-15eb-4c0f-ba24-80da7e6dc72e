#!/usr/bin/env python3
"""
Test the QA Generator with the 5-chunk subset
Supports parameterized LLM selection
"""

import sys
import os
import argparse
from pathlib import Path
from dotenv import load_dotenv

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider

def get_llm_config(provider: str, api_key: str = None, model: str = None) -> dict:
    """Get LLM configuration based on provider."""
    
    configs = {
        'google': {
            'llm_provider': LLMProvider.GOOGLE,
            'api_key': api_key or os.getenv('GOOGLE_API_KEY', ''),
            'model_name': model or os.getenv('GOOGLE_MODEL', 'gemini-2.0-flash-exp'),
            'base_url': None
        },
        'openai': {
            'llm_provider': LLMProvider.OPENAI,
            'api_key': api_key or os.getenv('OPENAI_API_KEY', ''),
            'model_name': model or os.getenv('OPENAI_MODEL', 'gpt-4o'),
            'base_url': None
        },
        'anthropic': {
            'llm_provider': LLMProvider.ANTHROPIC,
            'api_key': api_key or os.getenv('ANTHROPIC_API_KEY', ''),
            'model_name': model or os.getenv('ANTHROPIC_MODEL', 'claude-3-5-sonnet-20241022'),
            'base_url': None
        },
        'lm_studio': {
            'llm_provider': LLMProvider.LM_STUDIO,
            'api_key': '',  # Not needed for LM Studio
            'model_name': model or os.getenv('LM_STUDIO_MODEL', 'cognitivecomputations/dolphin-mistral-24b-venice-edition'),
            'base_url': os.getenv('LM_STUDIO_BASE_URL', 'http://localhost:1234/v1')
        },
        'openrouter': {
            'llm_provider': LLMProvider.OPENROUTER,
            'api_key': api_key or os.getenv('OPENROUTER_API_KEY', ''),
            'model_name': model or os.getenv('OPENROUTER_MODEL', 'deepseek/deepseek-chat-v3.1:free'),
            'base_url': None
        }
    }
    
    if provider not in configs:
        raise ValueError(f"Unsupported provider: {provider}. Supported: {list(configs.keys())}")
    
    return configs[provider]

def test_qa_with_chunks(provider: str = 'lm_studio', api_key: str = None, model: str = None):
    """Test QA generation with the 5-chunk subset using parameterized LLM selection."""
    
    print(f"🧪 Testing QA Generator with 5-chunk subset")
    print(f"   LLM Provider: {provider}")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv()
    
    # Check if test chunks file exists
    test_chunks_file = "test_chunks_5.json"
    if not Path(test_chunks_file).exists():
        print(f"❌ Test chunks file {test_chunks_file} not found!")
        print("Please run: python ../create_test_subset.py")
        return 1
    
    # Get LLM configuration
    try:
        llm_config = get_llm_config(provider, api_key, model)
    except ValueError as e:
        print(f"❌ {str(e)}")
        return 1
    
    # Validate API key for cloud providers
    if provider not in ['lm_studio'] and not llm_config['api_key']:
        print(f"❌ API key required for {provider}. Set environment variable or use --api-key")
        return 1
    
    # Create configuration
    config = QAGenerationConfig(
        llm_provider=llm_config['llm_provider'],
        api_key=llm_config['api_key'],
        model_name=llm_config['model_name'],
        base_url=llm_config['base_url'] or "http://localhost:1234/v1",
        questions_per_chunk=1,  # 1 question per chunk for testing
        batch_size=3,  # Process 3 chunks at a time
        enable_cross_chunk=False,  # Disable cross-chunk for testing
        min_answer_length=30,
        max_answer_length=300
    )
    
    print(f"📋 Configuration:")
    print(f"   Provider: {config.llm_provider.value}")
    print(f"   Model: {config.model_name}")
    print(f"   Base URL: {config.base_url}")
    print(f"   Questions per chunk: {config.questions_per_chunk}")
    print(f"   Batch size: {config.batch_size}")
    
    try:
        # Initialize generator
        print(f"\\n🚀 Initializing QA Generator...")
        generator = QAGenerator(config)
        
        # Load test chunks
        print(f"📖 Loading test chunks from {test_chunks_file}...")
        chunks = generator.load_chunks(test_chunks_file)
        print(f"   Loaded {len(chunks)} chunks")
        
        # Show chunk details
        print(f"\\n📊 Chunk details:")
        for i, chunk in enumerate(chunks, 1):
            sections_count = len(chunk.sections)
            spans_count = sum(1 for s in chunk.sections if 'context_span' in s)
            print(f"   {i}. {chunk.id}")
            print(f"      - {sections_count} sections, {spans_count} with spans")
            print(f"      - {chunk.word_count} words, {chunk.character_count} chars")
            print(f"      - Summary: {chunk.contextual_summary[:60]}...")
        
        # Generate QA triplets
        print(f"\\n🧠 Generating QA triplets...")
        triplets = generator.generate_qa_triplets()
        
        # Save results
        output_file = "test_qa_output.json"
        print(f"\\n💾 Saving results to {output_file}...")
        generator.save_qa_triplets(output_file)
        
        print(f"\\n✅ Test completed successfully!")
        print(f"📈 Results:")
        print(f"   - Generated {len(triplets)} QA triplets")
        print(f"   - Output saved to {output_file}")
        
        # Show sample results
        if triplets:
            print(f"\\n📝 Sample QA triplet:")
            sample = triplets[0]
            print(f"   Question: {sample.question}")
            print(f"   Answer: {sample.answer[:100]}...")
            print(f"   Difficulty: {sample.difficulty.value}")
            print(f"   Type: {sample.question_type.value}")
            print(f"   Chunk IDs: {sample.chunk_ids}")
            if hasattr(sample, 'context_spans') and sample.context_spans:
                print(f"   Context Spans: {sample.context_spans}")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1

def main():
    """Main function with argument parsing for parameterized LLM selection."""
    parser = argparse.ArgumentParser(
        description='Test QA Generator with parameterized LLM selection',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Test with LM Studio (default)
  python test_qa_with_subset.py
  
  # Test with Google Gemini
  python test_qa_with_subset.py --provider google --api-key YOUR_KEY
  
  # Test with OpenAI
  python test_qa_with_subset.py --provider openai --api-key YOUR_KEY --model gpt-4
  
  # Test with Anthropic
  python test_qa_with_subset.py --provider anthropic --api-key YOUR_KEY
  
  # Test with custom LM Studio model
  python test_qa_with_subset.py --provider lm_studio --model custom-model-name
"""
    )
    
    parser.add_argument(
        '--provider', 
        choices=['google', 'openai', 'anthropic', 'lm_studio', 'openrouter'],
        default='lm_studio',
        help='LLM provider to use (default: lm_studio)'
    )
    
    parser.add_argument(
        '--api-key',
        help='API key for the LLM provider (not needed for lm_studio)'
    )
    
    parser.add_argument(
        '--model',
        help='Model name to use (optional, uses defaults if not specified)'
    )
    
    parser.add_argument(
        '--list-providers',
        action='store_true',
        help='List available providers and their default models'
    )
    
    args = parser.parse_args()
    
    if args.list_providers:
        print("📋 Available LLM Providers:")
        print("   google     - Google Gemini (default: gemini-2.0-flash-exp)")
        print("   openai     - OpenAI GPT (default: gpt-4o)")
        print("   anthropic  - Anthropic Claude (default: claude-3-5-sonnet-20241022)")
        print("   lm_studio  - Local LM Studio (default: cognitivecomputations/dolphin-mistral-24b-venice-edition)")
        print("   openrouter - OpenRouter API (default: deepseek/deepseek-chat-v3.1:free)")
        print("\n🔑 API Key Requirements:")
        print("   google     - GOOGLE_API_KEY environment variable")
        print("   openai     - OPENAI_API_KEY environment variable")
        print("   anthropic  - ANTHROPIC_API_KEY environment variable")
        print("   openrouter - OPENROUTER_API_KEY environment variable")
        print("   lm_studio  - No API key needed (local inference)")
        return 0
    
    return test_qa_with_chunks(args.provider, args.api_key, args.model)

if __name__ == "__main__":
    sys.exit(main())