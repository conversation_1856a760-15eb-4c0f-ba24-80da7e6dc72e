# OpenRouter Integration for QA Generation

## Overview

OpenRouter integration has been successfully added to the QA Generator, providing access to the **DeepSeek Chat v3.1:free** model and many other models through a unified API.

## 🚀 Quick Start

### 1. Get OpenRouter API Key
1. Visit [https://openrouter.ai](https://openrouter.ai)
2. Sign up for a free account
3. Generate an API key from the dashboard

### 2. Configure Environment
```bash
# Add to your .env file
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=deepseek/deepseek-chat-v3.1:free
```

### 3. Run QA Generation
```bash
# Basic usage with OpenRouter
python run_qa_generator.py --provider openrouter --api-key YOUR_KEY

# Using environment variables
export OPENROUTER_API_KEY=your_key
python run_qa_generator.py --provider openrouter

# With custom model
python run_qa_generator.py --provider openrouter --api-key YOUR_KEY --model deepseek/deepseek-r1-distill-llama-70b
```

## 🆓 DeepSeek Chat v3.1:free Model

The default model `deepseek/deepseek-chat-v3.1:free` offers:

- **Zero Cost**: Completely free to use
- **High Quality**: Excellent reasoning capabilities for legal QA generation
- **Generous Limits**: Suitable for extensive document processing
- **Fast Response**: Quick inference times
- **Legal Text Optimized**: Works well with complex legal language

## 🔧 Implementation Details

### New Provider Class
```python
class OpenRouterProvider(BaseLLMProvider):
    """OpenRouter LLM provider for DeepSeek and other models."""
    
    def __init__(self, api_key: str, model_name: str = "deepseek/deepseek-chat-v3.1:free"):
        super().__init__(api_key, model_name)
        self.client = openai.OpenAI(
            api_key=api_key,
            base_url="https://openrouter.ai/api/v1"
        )
```

### Configuration Updates
- Added `OPENROUTER` to `LLMProvider` enum
- Updated `LLMProviderFactory` to handle OpenRouter
- Added environment variable support
- Updated all test scripts and documentation

### Files Modified
1. `qa_generator.py` - Core provider implementation
2. `run_qa_generator.py` - Command-line support
3. `test_qa_with_subset.py` - Testing integration
4. `.env.example` - Configuration template

## 🎯 Available Models

### Free Models
- `deepseek/deepseek-chat-v3.1:free` (default)
- `deepseek/deepseek-r1-distill-qwen-1.5b:free`
- `meta-llama/llama-3.2-1b-instruct:free`

### Premium Models
- `deepseek/deepseek-r1-distill-llama-70b`
- `anthropic/claude-3.5-sonnet`
- `openai/gpt-4o`
- `meta-llama/llama-3.1-405b-instruct`

## 💡 Benefits

1. **Cost Optimization**: Free tier with powerful models
2. **No Vendor Lock-in**: Easy switching between models
3. **Unified API**: Single interface for multiple providers
4. **Automatic Failover**: Built-in reliability features
5. **Competitive Pricing**: Cost-effective paid options

## 🧪 Testing

### Test OpenRouter Integration
```bash
cd QA_Generator
python test_openrouter_integration.py
```

### Test with Subset
```bash
python test_qa_with_subset.py --provider openrouter --api-key YOUR_KEY
```

### Demo Integration
```bash
python demo_openrouter_integration.py
```

## 📋 Configuration Examples

### Environment Variables (.env)
```env
# OpenRouter Configuration
OPENROUTER_API_KEY=sk-or-v1-your-key-here
OPENROUTER_MODEL=deepseek/deepseek-chat-v3.1:free

# Alternative models
# OPENROUTER_MODEL=deepseek/deepseek-r1-distill-llama-70b
# OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
```

### Command Line Usage
```bash
# Use default free model
python run_qa_generator.py --provider openrouter --api-key YOUR_KEY

# Use premium model
python run_qa_generator.py --provider openrouter --api-key YOUR_KEY --model deepseek/deepseek-r1-distill-llama-70b

# Process specific file
python run_qa_generator.py --provider openrouter --api-key YOUR_KEY --input my_chunks.json
```

## 🔍 Integration Status

✅ **Completed Features:**
- OpenRouter provider implementation
- DeepSeek Chat v3.1:free model integration
- Command-line interface support
- Environment variable configuration
- Test scripts and validation
- Documentation and examples
- Error handling and validation

✅ **Tested Components:**
- Provider initialization
- Model configuration
- API communication
- QA triplet generation
- Error handling
- Configuration validation

## 🚀 Ready for Production

The OpenRouter integration is fully functional and ready for use. It provides a cost-effective alternative for legal document QA generation while maintaining the same high quality as other providers.

### Recommended Usage
- **Development/Testing**: Use `deepseek/deepseek-chat-v3.1:free` for cost-free experimentation
- **Production**: Consider premium models for critical applications
- **Hybrid Approach**: Combine with local LM Studio for maximum flexibility

The integration follows the same patterns as existing providers, ensuring consistency and reliability across the entire QA generation system.