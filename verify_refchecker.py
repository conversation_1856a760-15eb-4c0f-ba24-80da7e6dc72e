#!/usr/bin/env python3
"""
RefChecker Integration Verification Script
==========================================

This script verifies that the RefChecker integration is properly set up
and all components can be imported and initialized correctly.
"""

import sys
import importlib

def test_imports():
    """Test that all RefChecker components can be imported."""
    print("🔍 Testing RefChecker Integration Imports...")
    
    try:
        # Test core module imports
        from refchecker_validator import (
            RefCheckerValidator, RefCheckerConfig, CheckerType, 
            ValidationResult, Claim, ValidationReport, create_default_config
        )
        print("✅ RefChecker validator components imported successfully")
        
        # Test QA generator integration
        from qa_generator import QAGenerator, QAGenerationConfig, LLMProvider
        print("✅ QA Generator components imported successfully")
        
        # Test demo script imports
        from demo_refchecker import (
            create_sample_qa_triplets, demo_basic_refchecker,
            create_sample_chunks_file, analyze_validation_report
        )
        print("✅ Demo script components imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_configuration():
    """Test RefChecker configuration creation."""
    print("\n🔧 Testing RefChecker Configuration...")
    
    try:
        from refchecker_validator import RefCheckerConfig, CheckerType, create_default_config
        
        # Test default config
        config = create_default_config()
        assert config.checker_type == CheckerType.LLM_CHECKER
        assert config.model_name == "gpt-4o"
        assert config.factuality_threshold == 0.7
        print("✅ Default configuration created successfully")
        
        # Test custom config
        custom_config = RefCheckerConfig(
            checker_type=CheckerType.LLM_CHECKER,
            model_name="gpt-4o",
            api_key="test-key",
            factuality_threshold=0.8,
            max_hallucination_rate=0.2
        )
        assert custom_config.factuality_threshold == 0.8
        print("✅ Custom configuration created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_qa_generator_integration():
    """Test QA Generator RefChecker integration."""
    print("\n🤖 Testing QA Generator RefChecker Integration...")
    
    try:
        from qa_generator import QAGenerationConfig, LLMProvider
        
        # Test QA config with RefChecker enabled
        config = QAGenerationConfig(
            llm_provider=LLMProvider.GOOGLE,
            api_key="test-key",
            model_name="gemini-2.0-flash-exp",
            enable_refchecker=True,
            refchecker_model="gpt-4o",
            refchecker_api_key="test-key",
            factuality_threshold=0.7,
            max_hallucination_rate=0.3
        )
        
        assert config.enable_refchecker == True
        assert config.refchecker_model == "gpt-4o"
        assert config.factuality_threshold == 0.7
        print("✅ QA Generator RefChecker configuration successful")
        
        return True
        
    except Exception as e:
        print(f"❌ QA Generator integration test failed: {e}")
        return False


def test_sample_data():
    """Test sample data creation."""
    print("\n📝 Testing Sample Data Creation...")
    
    try:
        from demo_refchecker import create_sample_qa_triplets
        
        # Test sample QA triplets
        qa_triplets = create_sample_qa_triplets()
        assert len(qa_triplets) == 3
        assert all("id" in qa for qa in qa_triplets)
        assert all("question" in qa for qa in qa_triplets)
        assert all("answer" in qa for qa in qa_triplets)
        assert all("context" in qa for qa in qa_triplets)
        print("✅ Sample QA triplets created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Sample data test failed: {e}")
        return False


def check_dependencies():
    """Check if optional dependencies are available."""
    print("\n📦 Checking Dependencies...")
    
    # Check RefChecker availability
    try:
        import refchecker
        print("✅ RefChecker package is available")
        refchecker_available = True
    except ImportError:
        print("⚠️  RefChecker package not installed (this is expected)")
        print("   Install with: pip install refchecker")
        refchecker_available = False
    
    # Check spaCy availability
    try:
        import spacy
        print("✅ spaCy package is available")
        spacy_available = True
    except ImportError:
        print("⚠️  spaCy package not installed")
        print("   Install with: pip install spacy")
        spacy_available = False
    
    # Check if spaCy model is available
    if spacy_available:
        try:
            spacy.load("en_core_web_sm")
            print("✅ spaCy English model is available")
        except OSError:
            print("⚠️  spaCy English model not installed")
            print("   Install with: python -m spacy download en_core_web_sm")
    
    return refchecker_available, spacy_available


def main():
    """Run all verification tests."""
    print("🚀 RefChecker Integration Verification")
    print("=" * 50)
    
    # Track test results
    results = []
    
    # Run tests
    results.append(("Imports", test_imports()))
    results.append(("Configuration", test_configuration()))
    results.append(("QA Generator Integration", test_qa_generator_integration()))
    results.append(("Sample Data", test_sample_data()))
    
    # Check dependencies
    print("\n" + "=" * 50)
    refchecker_available, spacy_available = check_dependencies()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Verification Summary")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name:<25} {status}")
    
    print(f"\nCore Integration: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 RefChecker integration is properly set up!")
        print("\n📋 Next Steps:")
        if not refchecker_available:
            print("  1. Install RefChecker: pip install refchecker")
        if not spacy_available:
            print("  2. Install spaCy: pip install spacy")
            print("  3. Download model: python -m spacy download en_core_web_sm")
        else:
            print("  1. Set up API keys (OPENAI_API_KEY, GOOGLE_API_KEY)")
            print("  2. Run demo: python demo_refchecker.py --demo basic --api-key YOUR_KEY")
            print("  3. Test with real data: python demo_refchecker.py --demo full --chunks-file test_chunks_5.json --api-key YOUR_KEY")
    else:
        print(f"\n❌ {total - passed} test(s) failed. Please check the errors above.")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())