#!/usr/bin/env python3
"""
RefChecker Report Analyzer
=========================

Analyzes RefChecker validation reports to show detailed information
about failed triplets and contradictory claims.
"""

import json
import sys

def analyze_refchecker_report(report_file):
    """Analyze RefChecker validation report."""
    with open(report_file, 'r', encoding='utf-8') as f:
        report = json.load(f)
    
    print("🔍 RefChecker Validation Report Analysis")
    print("=" * 60)
    
    # Overall statistics
    print(f"\n📊 Overall Statistics:")
    print(f"  Validation timestamp: {report['validation_timestamp']}")
    print(f"  Model used: {report['model_used']}")
    print(f"  Total triplets validated: {report['total_triplets_validated']}")
    print(f"  ✅ Passed validation: {report['passed_validation']} ({report['success_rate']*100:.1f}%)")
    print(f"  ❌ Failed validation: {report['failed_validation']} ({(1-report['success_rate'])*100:.1f}%)")
    print(f"  📈 Average factuality score: {report['average_factuality_score']:.3f}")
    
    # Failed triplets analysis
    failed_triplets = [r for r in report['detailed_reports'] if not r['passed_validation']]
    
    if failed_triplets:
        print(f"\n❌ Failed Triplets with Contradictory Claims ({len(failed_triplets)} triplets):")
        print("=" * 60)
        
        for i, triplet in enumerate(failed_triplets, 1):
            print(f"\n[{i}] QA Triplet ID: {triplet['qa_triplet_id']}")
            print(f"    Factuality Score: {triplet['overall_factuality_score']:.2f}")
            print(f"    Hallucination Rate: {triplet['hallucination_rate']:.2%}")
            print(f"    Claims: {triplet['factual_claims']} factual, {triplet['hallucinated_claims']} hallucinated, {triplet['neutral_claims']} neutral")
            
            # Show hallucinated claims
            hallucinated_claims = [c for c in triplet['claims'] if c['validation_result'] == 'hallucinated']
            if hallucinated_claims:
                print(f"\n    🔴 Contradictory/Hallucinated Claims:")
                for j, claim in enumerate(hallucinated_claims, 1):
                    print(f"      {j}. \"{claim['content']}\"")
                    print(f"         Confidence: {claim['confidence_score']:.2f}")
                    print(f"         Reasoning: {claim['reasoning']}")
                    print()
            
            # Show factual claims for context
            factual_claims = [c for c in triplet['claims'] if c['validation_result'] == 'factual']
            if factual_claims:
                print(f"    ✅ Factual Claims (for context):")
                for j, claim in enumerate(factual_claims, 1):
                    print(f"      {j}. \"{claim['content']}\"")
                print()
    else:
        print(f"\n🎉 No Failed Triplets!")
        print("All QA triplets passed validation - no contradictory claims found.")
    
    # Top performers
    passed_triplets = [r for r in report['detailed_reports'] if r['passed_validation']]
    if passed_triplets:
        # Sort by factuality score
        top_performers = sorted(passed_triplets, key=lambda x: x['overall_factuality_score'], reverse=True)
        
        print(f"\n✅ Top Performing Triplets (highest factuality):")
        print("-" * 40)
        for i, triplet in enumerate(top_performers[:5], 1):
            print(f"  {i}. {triplet['qa_triplet_id']}: {triplet['overall_factuality_score']:.2f} factuality, {len(triplet['claims'])} claims")
    
    # Claims analysis
    all_claims = []
    for triplet in report['detailed_reports']:
        all_claims.extend(triplet['claims'])
    
    total_claims = len(all_claims)
    factual_claims = sum(1 for c in all_claims if c['validation_result'] == 'factual')
    hallucinated_claims = sum(1 for c in all_claims if c['validation_result'] == 'hallucinated')
    neutral_claims = sum(1 for c in all_claims if c['validation_result'] == 'neutral')
    
    print(f"\n🔍 Claim-Level Analysis:")
    print(f"  Total claims analyzed: {total_claims}")
    print(f"  ✅ Factual: {factual_claims} ({factual_claims/total_claims*100:.1f}%)")
    print(f"  ❌ Hallucinated: {hallucinated_claims} ({hallucinated_claims/total_claims*100:.1f}%)")
    print(f"  ❓ Neutral: {neutral_claims} ({neutral_claims/total_claims*100:.1f}%)")
    
    return failed_triplets

def main():
    if len(sys.argv) != 2:
        print("Usage: python analyze_refchecker_report.py <report_file.json>")
        return 1
    
    report_file = sys.argv[1]
    
    try:
        failed_triplets = analyze_refchecker_report(report_file)
        
        if failed_triplets:
            print(f"\n💡 Summary:")
            print(f"Found {len(failed_triplets)} QA triplets with contradictory claims.")
            print(f"These triplets contain answers that make assertions not fully supported by the source context.")
        else:
            print(f"\n💡 Summary:")
            print(f"All QA triplets are high quality with no contradictory claims detected.")
        
        return 0
        
    except Exception as e:
        print(f"❌ Error analyzing report: {e}")
        return 1

if __name__ == '__main__':
    exit(main())