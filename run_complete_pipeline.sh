#!/bin/bash
# Complete QA Generation and Validation Pipeline
# Run this script to execute the full workflow

echo "🚀 Starting Complete QA Generation and Validation Pipeline"
echo "=========================================================="

# Step 1: Generate QA Triplets
echo "📝 Step 1: Generating QA Triplets with Evidence..."
python run_qa_generator.py --chunks-file test_chunks_5.json --output pipeline_qa_triplets.json

if [ $? -eq 0 ]; then
    echo "✅ QA triplets generated successfully"
else
    echo "❌ QA generation failed"
    exit 1
fi

# Step 2: Validate with RefChecker
echo ""
echo "🔍 Step 2: Validating QA Triplets with RefChecker..."
python lightweight_refchecker.py --qa-file pipeline_qa_triplets.json --model gpt-4o-mini

if [ $? -eq 0 ]; then
    echo "✅ RefChecker validation completed"
else
    echo "❌ RefChecker validation failed"
    exit 1
fi

# Step 3: Find the latest validation report
echo ""
echo "📊 Step 3: Analyzing Validation Results..."
LATEST_REPORT=$(ls -t refchecker_validation_report_*.json 2>/dev/null | head -n1)

if [ -n "$LATEST_REPORT" ]; then
    echo "📄 Found validation report: $LATEST_REPORT"
    python analyze_refchecker_report.py "$LATEST_REPORT"
    echo "✅ Analysis completed"
else
    echo "⚠️ No validation report found, running simple analysis..."
    python analyze_qa_triplets.py --qa-file pipeline_qa_triplets.json
fi

echo ""
echo "🎉 Pipeline completed successfully!"
echo "📂 Generated files:"
echo "   - QA Triplets: pipeline_qa_triplets.json"
echo "   - Validation Report: $LATEST_REPORT"
echo "   - Analysis: See console output above"