#!/usr/bin/env python3
"""
Simple QA Triplet Analysis Tool
===============================

Analyzes existing QA triplets to identify potential hallucinations by checking:
1. Answer claims not supported by context
2. Overly specific details not in source
3. Missing context references
"""

import json
import re
from datetime import datetime

def load_qa_triplets(file_path):
    """Load QA triplets from JSON file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data.get('qa_triplets', [])

def extract_simple_claims(answer):
    """Extract simple claims from answer text."""
    # Split by sentence endings
    sentences = re.split(r'[.!?]+', answer)
    claims = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
    return claims

def analyze_qa_triplet(qa_triplet):
    """Analyze a QA triplet for potential issues."""
    analysis = {
        "id": qa_triplet.get("id", "unknown"),
        "question": qa_triplet.get("question", "")[:100] + "..." if len(qa_triplet.get("question", "")) > 100 else qa_triplet.get("question", ""),
        "answer": qa_triplet.get("answer", "")[:150] + "..." if len(qa_triplet.get("answer", "")) > 150 else qa_triplet.get("answer", ""),
        "potential_issues": [],
        "claims": [],
        "risk_score": 0
    }
    
    answer = qa_triplet.get("answer", "")
    context = qa_triplet.get("context", [])
    
    # Extract context text
    context_text = ""
    if isinstance(context, list):
        context_text = " ".join([item.get("content", "") if isinstance(item, dict) else str(item) for item in context])
    else:
        context_text = str(context)
    
    # Extract claims
    claims = extract_simple_claims(answer)
    analysis["claims"] = claims
    
    # Check for potential issues
    
    # 1. Answer much longer than context (potential hallucination)
    if len(answer) > len(context_text) * 1.5:
        analysis["potential_issues"].append("Answer significantly longer than context")
        analysis["risk_score"] += 20
    
    # 2. Specific numbers not in context
    answer_numbers = re.findall(r'\b\d+\b', answer)
    context_numbers = re.findall(r'\b\d+\b', context_text)
    unsupported_numbers = [num for num in answer_numbers if num not in context_numbers]
    if unsupported_numbers:
        analysis["potential_issues"].append(f"Numbers not in context: {unsupported_numbers}")
        analysis["risk_score"] += 15 * len(unsupported_numbers)
    
    # 3. Proper nouns not in context
    answer_proper_nouns = re.findall(r'\b[A-Z][a-zA-Z]+\b', answer)
    context_proper_nouns = re.findall(r'\b[A-Z][a-zA-Z]+\b', context_text)
    unsupported_proper_nouns = [noun for noun in answer_proper_nouns if noun not in context_proper_nouns]
    if unsupported_proper_nouns:
        analysis["potential_issues"].append(f"Proper nouns not in context: {unsupported_proper_nouns}")
        analysis["risk_score"] += 10 * len(unsupported_proper_nouns)
    
    # 4. Very specific details (dates, percentages) that might be hallucinated
    specific_patterns = [
        r'\b\d{1,2}/\d{1,2}/\d{4}\b',  # dates
        r'\b\d+%\b',                    # percentages
        r'\b\d+\.\d+ million\b',        # specific amounts
        r'\bexactly \d+\b',             # exact numbers
    ]
    
    for pattern in specific_patterns:
        answer_matches = re.findall(pattern, answer)
        context_matches = re.findall(pattern, context_text)
        unsupported = [match for match in answer_matches if match not in context_matches]
        if unsupported:
            analysis["potential_issues"].append(f"Specific details not in context: {unsupported}")
            analysis["risk_score"] += 25
    
    # 5. Empty or very short context
    if len(context_text.strip()) < 50:
        analysis["potential_issues"].append("Very short context for answer")
        analysis["risk_score"] += 30
    
    # 6. No direct quotes or references
    if "article" in answer.lower() or "art." in answer.lower():
        # Legal answer should reference specific articles
        if not any(ref in context_text.lower() for ref in ["art.", "article"]):
            analysis["potential_issues"].append("Answer mentions articles not clearly in context")
            analysis["risk_score"] += 15
    
    # Determine risk level
    if analysis["risk_score"] >= 50:
        analysis["risk_level"] = "HIGH"
    elif analysis["risk_score"] >= 25:
        analysis["risk_level"] = "MEDIUM"
    else:
        analysis["risk_level"] = "LOW"
    
    return analysis

def generate_analysis_report(qa_triplets, output_file=None):
    """Generate analysis report for QA triplets."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if not output_file:
        output_file = f"qa_analysis_report_{timestamp}.json"
    
    print(f"🔍 Analyzing {len(qa_triplets)} QA triplets...")
    
    # Analyze each triplet
    analyses = []
    high_risk_count = 0
    medium_risk_count = 0
    low_risk_count = 0
    
    for qa_triplet in qa_triplets:
        analysis = analyze_qa_triplet(qa_triplet)
        analyses.append(analysis)
        
        if analysis["risk_level"] == "HIGH":
            high_risk_count += 1
        elif analysis["risk_level"] == "MEDIUM":
            medium_risk_count += 1
        else:
            low_risk_count += 1
    
    # Create report
    report = {
        "analysis_timestamp": datetime.now().isoformat(),
        "total_triplets_analyzed": len(qa_triplets),
        "risk_distribution": {
            "high_risk": high_risk_count,
            "medium_risk": medium_risk_count,
            "low_risk": low_risk_count
        },
        "high_risk_rate": high_risk_count / len(qa_triplets) if qa_triplets else 0,
        "detailed_analyses": analyses
    }
    
    # Save report
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # Print summary
    print(f"\n📊 Analysis Summary:")
    print(f"  Total triplets: {len(qa_triplets)}")
    print(f"  🔴 High risk: {high_risk_count} ({high_risk_count/len(qa_triplets)*100:.1f}%)")
    print(f"  🟡 Medium risk: {medium_risk_count} ({medium_risk_count/len(qa_triplets)*100:.1f}%)")
    print(f"  🟢 Low risk: {low_risk_count} ({low_risk_count/len(qa_triplets)*100:.1f}%)")
    
    # Show high-risk triplets
    high_risk_triplets = [a for a in analyses if a["risk_level"] == "HIGH"]
    if high_risk_triplets:
        print(f"\n🔴 High-Risk Triplets (potential hallucinations):")
        for analysis in high_risk_triplets[:5]:  # Show first 5
            print(f"  - {analysis['id']}: Risk Score {analysis['risk_score']}")
            print(f"    Question: {analysis['question']}")
            print(f"    Issues: {', '.join(analysis['potential_issues'])}")
            print()
    
    print(f"📄 Detailed report saved to: {output_file}")
    return output_file

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Analyze QA triplets for potential hallucinations')
    parser.add_argument('--qa-file', required=True, help='QA triplets JSON file to analyze')
    parser.add_argument('--output', help='Output report file (auto-generated if not specified)')
    
    args = parser.parse_args()
    
    print("🔍 Simple QA Triplet Analysis Tool")
    print("=" * 50)
    
    try:
        # Load QA triplets
        print(f"📂 Loading QA triplets from: {args.qa_file}")
        qa_triplets = load_qa_triplets(args.qa_file)
        print(f"✅ Loaded {len(qa_triplets)} QA triplets")
        
        # Generate analysis report
        report_file = generate_analysis_report(qa_triplets, args.output)
        
        return 0
        
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == '__main__':
    exit(main())