#!/usr/bin/env python3
"""
Fix LM Studio context overflow by updating configuration
"""

import os
import shutil
from pathlib import Path

def fix_lm_studio_context():
    """Update .env configuration to work with 4K context limit."""
    
    print("🔧 Fixing LM Studio Context Overflow")
    print("=" * 40)
    
    # Check if .env exists
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env file not found. Copying from .env.lm_studio template...")
        
        # Copy the optimized LM Studio configuration
        if Path(".env.lm_studio").exists():
            shutil.copy(".env.lm_studio", ".env")
            print("✅ Copied optimized LM Studio configuration to .env")
        else:
            print("❌ .env.lm_studio template not found!")
            return False
    else:
        print("📁 Found existing .env file")
        
        # Read current .env
        with open(".env", "r") as f:
            lines = f.readlines()
        
        # Update key parameters for 4K context
        updated_lines = []
        updates_made = []
        
        for line in lines:
            if line.startswith("BATCH_SIZE="):
                updated_lines.append("BATCH_SIZE=3\n")
                updates_made.append("BATCH_SIZE: 10 → 3")
            elif line.startswith("QUESTIONS_PER_CHUNK="):
                updated_lines.append("QUESTIONS_PER_CHUNK=1\n")
                updates_made.append("QUESTIONS_PER_CHUNK: 2 → 1")
            else:
                updated_lines.append(line)
        
        # Write updated .env
        with open(".env", "w") as f:
            f.writelines(updated_lines)
        
        if updates_made:
            print("🔄 Updated configuration:")
            for update in updates_made:
                print(f"   • {update}")
        else:
            print("ℹ️  Configuration already optimized")
    
    print("\n📋 Optimized Settings for 4K Context:")
    print("   • Batch Size: 3 chunks (reduced from 10)")
    print("   • Questions per Chunk: 1 (reduced from 2)")
    print("   • Content Truncation: 400 chars (reduced from 1000)")
    print("   • Summary Truncation: 200 chars (reduced from full)")
    
    print("\n🚀 Ready to run:")
    print("   python run_qa_generator.py")
    
    return True

if __name__ == "__main__":
    fix_lm_studio_context()